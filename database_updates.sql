-- 问卷系统表结构调整 SQL
-- 执行日期：2025-06-29

-- 1. 为 DC_COURSE_PAPER_HISTORY_ALL 表添加主键
-- 首先添加新的主键列
ALTER TABLE DC_COURSE_PAPER_HISTORY_ALL ADD COLUMN HISTORY_ALL_ID VARCHAR(64) NOT NULL COMMENT '主键ID';

-- 为新主键列生成唯一值（使用UUID或雪花ID）
UPDATE DC_COURSE_PAPER_HISTORY_ALL SET HISTORY_ALL_ID = REPLACE(UUID(), '-', '') WHERE HISTORY_ALL_ID IS NULL OR HISTORY_ALL_ID = '';

-- 设置主键
ALTER TABLE DC_COURSE_PAPER_HISTORY_ALL ADD PRIMARY KEY (HISTORY_ALL_ID);

-- 2. 如果需要，可以为 PAPER_ID 添加索引以提高查询性能
CREATE INDEX IDX_DC_COURSE_PAPER_HISTORY_ALL_PAPER_ID ON DC_COURSE_PAPER_HISTORY_ALL(PAPER_ID);

-- 3. 为 COURSE_ID 添加索引
CREATE INDEX IDX_DC_COURSE_PAPER_HISTORY_ALL_COURSE_ID ON DC_COURSE_PAPER_HISTORY_ALL(COURSE_ID);

-- 4. 检查并确保其他相关表的索引
-- 为 DC_COURSE_PAPER_HISTORY 表添加索引
CREATE INDEX IDX_DC_COURSE_PAPER_HISTORY_PAPER_ID ON DC_COURSE_PAPER_HISTORY(PAPER_ID);
CREATE INDEX IDX_DC_COURSE_PAPER_HISTORY_STUDENT_CODE ON DC_COURSE_PAPER_HISTORY(STUDENT_CODE);

-- 为 DC_COURSE_QUESTION_HISTORY 表添加索引
CREATE INDEX IDX_DC_COURSE_QUESTION_HISTORY_PH_ID ON DC_COURSE_QUESTION_HISTORY(PH_ID);
CREATE INDEX IDX_DC_COURSE_QUESTION_HISTORY_QUESTION_ID ON DC_COURSE_QUESTION_HISTORY(QUESTION_ID);

-- 为 DC_COURSE_QUESTION 表添加索引
CREATE INDEX IDX_DC_COURSE_QUESTION_PAPER_ID ON DC_COURSE_QUESTION(PAPER_ID);

-- 为 DC_COURSE_QUESTION_ITEM 表添加索引
CREATE INDEX IDX_DC_COURSE_QUESTION_ITEM_QUESTION_ID ON DC_COURSE_QUESTION_ITEM(QUESTION_ID);

-- 注意：执行前请备份数据库！
-- 建议在测试环境先执行验证无误后再在生产环境执行

-- 5. 数据迁移说明
-- 如果需要从旧的场景级别问卷迁移到课程级别问卷，需要：
-- a) 将 DC_SCENE_TESTPAPER 的数据迁移到 DC_COURSE_TESTPAPER
-- b) 将 DC_SCENE_QUESTION 的数据迁移到 DC_COURSE_QUESTION
-- c) 将 DC_SCENE_QUESTION_ITEM 的数据迁移到 DC_COURSE_QUESTION_ITEM
-- d) 创建对应的 DC_COURSE_PAPER_HISTORY_ALL 记录
-- 具体迁移脚本需要根据实际业务需求编写
