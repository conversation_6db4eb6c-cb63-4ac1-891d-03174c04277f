# 问卷系统重构总结

## 重构概述

根据新的业务需求，问卷系统已从场景级别重构为课程级别，实现了问卷、题目等与场景的隔离，现在都是course相关的实例化。

## 新的表结构关系

```
DC_COURSE_TESTPAPER (课程下定义的问卷)
    ↓ 1:1
DC_COURSE_PAPER_HISTORY_ALL (问卷发布实例)
    ↓ 1:N  
DC_COURSE_PAPER_HISTORY (问卷下发人员)

DC_COURSE_PAPER_QUESTION_HISTORY (回答记录)
```

## 主要修改内容

### 1. 实体类调整

#### 修改的实体类：
- `DcCoursePaperHistoryAll.java` - 添加了独立主键 `HISTORY_ALL_ID`，`PAPER_ID` 改为外键

#### 新增的学生端实体类：
- `StDcCourseTestpaper.java` - 学生端课程问卷实体
- `StDcCourseQuestion.java` - 学生端课程题目实体  
- `StDcCourseQuestionItem.java` - 学生端课程题目选项实体

### 2. 数据库结构调整

#### 主要变更：
- `DC_COURSE_PAPER_HISTORY_ALL` 表添加主键 `HISTORY_ALL_ID`
- 添加了多个索引以提高查询性能

#### SQL执行脚本：
详见 `database_updates.sql` 文件

### 3. Mapper XML 更新

#### 教师端 - TcSurveyMapper.xml：
- 所有查询从场景级别表改为课程级别表
- 使用 `DC_COURSE_TESTPAPER`、`DC_COURSE_QUESTION`、`DC_COURSE_QUESTION_ITEM`
- 通过 `DC_COURSE_PAPER_HISTORY_ALL` 关联课程和问卷

#### 学生端 - StSurveyMapper.xml：
- 更新查询逻辑，支持通过场景ID查找相关课程的问卷
- 修改 resultMap 使用新的课程级别实体类
- 保持向后兼容，学生端仍可通过场景ID查询问卷

### 4. Mapper接口更新

#### 新增方法：
- `DcCourseQuestionMapper` - 添加了 `getLagQuestion` 和 `getLeadQuestion` 方法

#### 修改的接口：
- `StSurveyMapper` - 更新返回类型为课程级别实体类

### 5. Service层更新

#### 修改的Service：
- `IStSurveyService` - 更新方法返回类型
- `StSurveyServiceImpl` - 更新实现逻辑

#### 修改的Controller：
- `StSurveyController` - 更新变量类型

### 6. 新增的Mapper XML文件

- `DcCourseQuestionMapper.xml` - 课程题目查询配置
- `DcCourseQuestionItemMapper.xml` - 课程题目选项查询配置（已存在但为空）

## 兼容性说明

### 向后兼容：
1. 学生端仍然可以通过场景ID查询问卷，系统会自动查找该场景下的课程问卷
2. 原有的场景级别表结构保持不变，作为模板使用
3. 问卷提交和答案记录的表结构保持不变

### 数据迁移：
如果需要从旧的场景级别问卷迁移到课程级别，需要：
1. 将 `DC_SCENE_TESTPAPER` 数据迁移到 `DC_COURSE_TESTPAPER`
2. 将 `DC_SCENE_QUESTION` 数据迁移到 `DC_COURSE_QUESTION`
3. 将 `DC_SCENE_QUESTION_ITEM` 数据迁移到 `DC_COURSE_QUESTION_ITEM`
4. 创建对应的 `DC_COURSE_PAPER_HISTORY_ALL` 记录

## 测试建议

### 必须测试的功能：
1. 教师端问卷列表查询
2. 教师端问卷详情查看
3. 教师端问卷统计数据
4. 学生端通过场景ID查询问卷列表
5. 学生端问卷详情查看和提交
6. 问卷答案统计和分析

### 测试数据准备：
1. 确保 `DC_COURSE_PAPER_HISTORY_ALL` 表有测试数据
2. 确保课程和场景的关联关系正确
3. 确保问卷下发记录存在

## 注意事项

1. **数据库备份**：执行SQL脚本前务必备份数据库
2. **索引创建**：新增的索引可能需要一定时间创建，建议在低峰期执行
3. **缓存清理**：重构后建议清理相关缓存
4. **日志监控**：部署后密切监控相关接口的调用情况

## 后续优化建议

1. 考虑添加问卷版本管理功能
2. 优化大数据量下的查询性能
3. 添加问卷模板复制功能
4. 考虑实现问卷的批量操作功能
