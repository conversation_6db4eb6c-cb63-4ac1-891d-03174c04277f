package com.ruoyi.project.student.controller;

import com.ruoyi.framework.aspectj.lang.annotation.Anonymous;
import com.ruoyi.framework.interceptor.annotation.RepeatSubmit;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.student.domain.StDcCourseTestpaper;
import com.ruoyi.project.student.domain.StLoginUser;
import com.ruoyi.project.student.domain.StSurveySubmission;
import com.ruoyi.project.student.service.IStSurveyService;
import com.ruoyi.project.student.util.StAuthUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 学生端问卷调查Controller
 * 
 * <AUTHOR>
 */
@Slf4j
@Anonymous
@RestController
@RequestMapping("/student/survey")
@RequiredArgsConstructor
public class StSurveyController {

    private final IStSurveyService stSurveyService;

    /**
     * 根据问卷ID查询问卷详情
     *
     * @param paperId 问卷ID
     * @return 问卷详情
     */
    @GetMapping("/{paperId}")
    public AjaxResult getSurveyDetail(@PathVariable String paperId) {
        // 检查用户是否已登录，未登录会抛出401异常
        StLoginUser loginUser = StAuthUtil.getRequiredLoginUser();

        StDcCourseTestpaper survey = stSurveyService.getSurveyDetail(paperId);
        
        // 检查学生是否已提交该问卷
        boolean hasSubmitted = stSurveyService.hasStudentSubmitted(paperId, loginUser.getUsername());
        
        // 构建返回结果
        AjaxResult result = AjaxResult.success(survey);
        result.put("hasSubmitted", hasSubmitted);
        
        return result;
    }

    /**
     * 提交问卷答案
     *
     * @param submission 提交数据
     * @return 提交结果
     */
    @PostMapping("/submit")
    @RepeatSubmit(interval = 10000, message = "请勿重复提交问卷")
    public AjaxResult submitSurvey(@RequestBody StSurveySubmission submission) {
        // 检查用户是否已登录，未登录会抛出401异常
        StLoginUser loginUser = StAuthUtil.getRequiredLoginUser();

        // 设置用户信息
        submission.setUserId(loginUser.getUserId());
        submission.setUsername(loginUser.getUsername());

        boolean success = stSurveyService.submitSurvey(submission);
        
        if (success) {
            return AjaxResult.success("提交成功");
        } else {
            return AjaxResult.error("提交失败");
        }
    }

    /**
     * 检查学生是否已提交该问卷
     *
     * @param paperId 问卷ID
     * @return 是否已提交
     */
    @GetMapping("/{paperId}/status")
    public AjaxResult checkSubmissionStatus(@PathVariable String paperId) {
        // 检查用户是否已登录，未登录会抛出401异常
        StLoginUser loginUser = StAuthUtil.getRequiredLoginUser();

        boolean hasSubmitted = stSurveyService.hasStudentSubmitted(paperId, loginUser.getUsername());
        
        return AjaxResult.success("查询成功", hasSubmitted);
    }

    /**
     * 根据场景ID查询问卷列表
     *
     * @param sceneId 场景ID
     * @return 问卷列表（包含问卷名称和ID）
     */
    @GetMapping("/scene/{sceneId}/list")
    public AjaxResult getSurveyListBySceneId(@PathVariable String sceneId) {
        // 检查用户是否已登录，未登录会抛出401异常
        StLoginUser loginUser = StAuthUtil.getRequiredLoginUser();

        // 使用新的方法获取包含提交状态的问卷列表
        List<Map<String, Object>> surveyList = stSurveyService.getSurveyListWithSubmissionStatus(sceneId, loginUser.getUsername());
        
        return AjaxResult.success("查询成功", surveyList);
    }
} 