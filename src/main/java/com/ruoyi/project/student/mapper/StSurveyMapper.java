package com.ruoyi.project.student.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.student.domain.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 学生端问卷调查 Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface StSurveyMapper extends BaseMapper<StDcCourseTestpaper> {

    /**
     * 根据问卷ID查询问卷详情（包含题目和选项）
     *
     * @param paperId 问卷ID
     * @return 问卷详情
     */
    StDcCourseTestpaper selectSurveyDetailById(@Param("paperId") String paperId);

    /**
     * 根据问卷ID查询题目列表
     *
     * @param paperId 问卷ID
     * @return 题目列表
     */
    List<StDcCourseQuestion> selectQuestionsByPaperId(@Param("paperId") String paperId);

    /**
     * 根据题目ID查询选项列表
     *
     * @param questionId 题目ID
     * @return 选项列表
     */
    List<StDcCourseQuestionItem> selectItemsByQuestionId(@Param("questionId") String questionId);

    /**
     * 检查学生是否已提交该问卷
     *
     * @param paperId 问卷ID
     * @param studentCode 学生代码
     * @return 是否已提交
     */
    boolean hasStudentSubmitted(@Param("paperId") String paperId, @Param("studentCode") String studentCode);

    /**
     * 根据场景ID查询问卷列表
     *
     * @param sceneId 场景ID
     * @return 问卷列表（包含问卷名称和ID）
     */
    List<Map<String, Object>> selectSurveyListBySceneId(@Param("sceneId") String sceneId);
    
    /**
     * 根据场景ID查询问卷列表（包含提交状态）
     *
     * @param sceneId 场景ID
     * @param studentCode 学生代码
     * @return 问卷列表（包含问卷名称、ID和提交状态）
     */
    List<Map<String, Object>> selectSurveyListWithSubmissionStatus(@Param("sceneId") String sceneId, @Param("studentCode") String studentCode);
} 