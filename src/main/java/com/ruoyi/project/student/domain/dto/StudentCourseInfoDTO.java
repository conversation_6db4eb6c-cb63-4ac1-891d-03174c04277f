package com.ruoyi.project.student.domain.dto;

import lombok.Data;

/**
 * 学生课程信息VO
 */
@Data
public class StudentCourseInfoDTO {
    
    /** 学号/用户名 */
    private String userName;
    
    /** 姓名 */
    private String nickName;
    
    /** 用户头像 */
    private String avatar;
    
    /** 课程分组ID */
    private String groupId;
    
    /** 课程分组名称 */
    private String groupName;
    
    /** 马甲名称 */
    private String puppetName;
    
    /** 马甲图标 */
    private String puppetIcon;
    
    /** 课程ID */
    private String courseId;
    
    /** 课程名称 */
    private String courseName;
    
    /** 课程介绍 */
    private String courseIntroduction;
    
    /** 场景ID */
    private String sceneId;
    
    /** 场景模板名称 */
    private String sceneName;
    
    /** 场景描述 */
    private String sceneIntroduction;
    
    /** 是否为课程大群（用于区分课程大群和普通分组） */
    private Boolean isPublic = false;
    
    /** 是否为组长 */
    private Boolean isGroupLeader = false;
} 