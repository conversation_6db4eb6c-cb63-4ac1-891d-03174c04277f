package com.ruoyi.project.student.dto;

import lombok.Data;

/**
 * 分组用户信息 DTO
 *
 * <AUTHOR>
 */
@Data
public class GroupUserDTO {

    /** 用户ID */
    private Long userId;

    /** 用户账号 (真实账号) */
    private String userName;

    /** 用户昵称 (真实昵称) */
    private String nickName;

    /** 用户头像 (真实头像) */
    private String avatar;

    /** 马甲名称 */
    private String puppetName;

    /** 马甲头像 */
    private String puppetIcon;

    /** 马甲排序号 */
    private String puppetIndex;

    /** 是否群管理 */
    private Boolean isGroupLeader;
} 