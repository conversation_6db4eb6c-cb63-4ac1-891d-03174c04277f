package com.ruoyi.project.student.service.impl;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.project.student.domain.StDcCourseTestpaper;
import com.ruoyi.project.student.domain.StSurveyAnswer;
import com.ruoyi.project.student.domain.StSurveySubmission;
import com.ruoyi.project.student.mapper.StSurveyAnswerMapper;
import com.ruoyi.project.student.mapper.StSurveyMapper;
import com.ruoyi.project.student.mapper.StSurveySubmissionMapper;
import com.ruoyi.project.student.service.IStSurveyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * 学生端问卷调查Service实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StSurveyServiceImpl implements IStSurveyService {

    private final StSurveyMapper stSurveyMapper;
    private final StSurveySubmissionMapper stSurveySubmissionMapper;
    private final StSurveyAnswerMapper stSurveyAnswerMapper;

    @Override
    public StDcCourseTestpaper getSurveyDetail(String paperId) {
        if (paperId == null || paperId.trim().isEmpty()) {
            throw new ServiceException("问卷ID不能为空");
        }

        StDcCourseTestpaper survey = stSurveyMapper.selectSurveyDetailById(paperId);
        if (survey == null) {
            throw new ServiceException("问卷不存在");
        }

        return survey;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitSurvey(StSurveySubmission submission) {
        if (submission == null) {
            throw new ServiceException("提交数据不能为空");
        }

        String paperId = submission.getPaperId();
        List<StSurveyAnswer> answers = submission.getAnswers();

        if (paperId == null || paperId.trim().isEmpty()) {
            throw new ServiceException("问卷ID不能为空");
        }

        if (CollectionUtils.isEmpty(answers)) {
            throw new ServiceException("答案不能为空");
        }

        // 获取当前登录用户信息
        Long userId = submission.getUserId();
        String username = submission.getUsername();

        if (userId == null || username == null || username.trim().isEmpty()) {
            throw new ServiceException("用户信息不能为空");
        }

        // 检查学生是否已经提交过该问卷
        if (hasStudentSubmitted(paperId, username)) {
            throw new ServiceException("您已经提交过该问卷，不能重复提交");
        }

        // 设置提交记录信息
        String phId = UUID.fastUUID().toString();
        submission.setPhId(phId);
        submission.setStudentCode(username);
        submission.setPaperSubmit("Y"); // 设置为已提交
        submission.setSubmitTime(new Date());
        submission.setCreateUser(username);
        submission.setCreateTime(new Date());

        try {
            // 1. 保存提交记录
            int submissionResult = stSurveySubmissionMapper.insert(submission);
            if (submissionResult <= 0) {
                throw new ServiceException("保存提交记录失败");
            }

            // 2. 保存答案详情
            for (StSurveyAnswer answer : answers) {
                answer.setPhId(phId);
                answer.setCreateUser(username);
                answer.setCreateTime(new Date());
                
                // 如果有selectedItems列表，转换为逗号分隔的字符串存储到myAnswer字段
                if (!CollectionUtils.isEmpty(answer.getSelectedItems())) {
                    answer.setMyAnswer(String.join(",", answer.getSelectedItems()));
                }

                int answerResult = stSurveyAnswerMapper.insert(answer);
                if (answerResult <= 0) {
                    throw new ServiceException("保存答案详情失败");
                }
            }

            log.info("学生 {} 成功提交问卷 {}，提交记录ID: {}", username, paperId, phId);
            return true;

        } catch (Exception e) {
            log.error("提交问卷失败: paperId={}, username={}, error={}", paperId, username, e.getMessage(), e);
            throw new ServiceException("提交问卷失败: " + e.getMessage());
        }
    }

    @Override
    public boolean hasStudentSubmitted(String paperId, String studentCode) {
        if (paperId == null || paperId.trim().isEmpty() || studentCode == null || studentCode.trim().isEmpty()) {
            return false;
        }
        
        return stSurveyMapper.hasStudentSubmitted(paperId, studentCode);
    }

    @Override
    public List<Map<String, Object>> getSurveyListBySceneId(String sceneId) {
        if (sceneId == null || sceneId.trim().isEmpty()) {
            throw new ServiceException("场景ID不能为空");
        }

        List<Map<String, Object>> surveyList = stSurveyMapper.selectSurveyListBySceneId(sceneId);
        
        log.info("根据场景ID {} 查询到 {} 个问卷", sceneId, surveyList != null ? surveyList.size() : 0);
        
        return surveyList != null ? surveyList : new ArrayList<>();
    }
    
    @Override
    public List<Map<String, Object>> getSurveyListWithSubmissionStatus(String sceneId, String studentCode) {
        if (sceneId == null || sceneId.trim().isEmpty()) {
            throw new ServiceException("场景ID不能为空");
        }
        
        if (studentCode == null || studentCode.trim().isEmpty()) {
            throw new ServiceException("学生代码不能为空");
        }
        
        List<Map<String, Object>> surveyList = stSurveyMapper.selectSurveyListWithSubmissionStatus(sceneId, studentCode);
        log.info("根据场景ID {} 和学生代码 {} 查询到 {} 个问卷", sceneId, studentCode, surveyList != null ? surveyList.size() : 0);
        
        return surveyList != null ? surveyList : new ArrayList<>();
    }
} 