package com.ruoyi.project.student.service;

import com.ruoyi.project.student.domain.StDcCourseTestpaper;
import com.ruoyi.project.student.domain.StSurveySubmission;

import java.util.List;
import java.util.Map;

/**
 * 学生端问卷调查Service接口
 * 
 * <AUTHOR>
 */
public interface IStSurveyService {

    /**
     * 根据问卷ID查询问卷详情（包含题目和选项）
     *
     * @param paperId 问卷ID
     * @return 问卷详情
     */
    StDcCourseTestpaper getSurveyDetail(String paperId);

    /**
     * 提交问卷答案
     *
     * @param submission 提交数据
     * @return 提交结果
     */
    boolean submitSurvey(StSurveySubmission submission);

    /**
     * 检查学生是否已提交该问卷
     *
     * @param paperId 问卷ID
     * @param studentCode 学生代码
     * @return 是否已提交
     */
    boolean hasStudentSubmitted(String paperId, String studentCode);

    /**
     * 根据场景ID查询问卷列表
     *
     * @param sceneId 场景ID
     * @return 问卷列表（包含问卷名称和ID）
     */
    List<Map<String, Object>> getSurveyListBySceneId(String sceneId);
    
    /**
     * 根据场景ID查询问卷列表（包含提交状态）
     *
     * @param sceneId 场景ID
     * @param studentCode 学生代码
     * @return 问卷列表（包含问卷名称、ID和提交状态）
     */
    List<Map<String, Object>> getSurveyListWithSubmissionStatus(String sceneId, String studentCode);
} 