package com.ruoyi.project.student.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.project.student.domain.StSysUser;
import com.ruoyi.project.student.domain.dto.StudentCourseInfoDTO;
import com.ruoyi.project.student.mapper.StSysUserMapper;
import com.ruoyi.project.student.service.IStSysUserService;
import com.ruoyi.project.system.domain.SysUser;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.stereotype.Service;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.manager.factory.AsyncFactory;
import com.ruoyi.project.student.domain.StLoginUser;
import com.ruoyi.project.student.util.StAuthUtil;
import com.ruoyi.project.student.vo.StLoginVo;
import com.ruoyi.project.student.dto.GroupUserDTO;
import com.ruoyi.project.student.service.IPuppetIconService;
import com.ruoyi.project.system.service.ISysConfigService;
import com.ruoyi.common.exception.ServiceException;

import java.util.List;
import java.util.ArrayList;

/**
 * 学生端系统用户 服务实现层
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class StSysUserServiceImpl implements IStSysUserService {

    private final StSysUserMapper stSysUserMapper;
    private final WxMaService wxMaService;
    private final ISysConfigService configService;
    private final IPuppetIconService puppetIconService;

    /**
     * 通过微信 code 获取用户信息并查找本地用户
     *
     * @param code 微信登录时获取的 code
     * @return 包含 openid 和 unionid 的 WxSessionInfo 对象，以及关联的本地 StSysUser (如果找到)
     */
    @Override
    public UserInfoWithWxSession loginByWechatCode(String code) {
        if (StringUtils.isEmpty(code)) {
            throw new ServiceException("微信登录凭证 code 不能为空");
        }

        WxMaJscode2SessionResult session = null;
        try {
            // 调用微信接口 jscode2session
            session = wxMaService.getUserService().getSessionInfo(code);
            log.info("微信 jscode2session 调用成功: openid={}, unionid={}", session.getOpenid(), session.getUnionid());

        } catch (WxErrorException e) {
            log.error("调用微信 jscode2session 接口失败: code={}, error={}", code, e.getMessage(), e);
            throw new ServiceException("调用微信登录接口失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("处理微信登录时发生未知错误: code={}", code, e);
            throw new ServiceException("处理微信登录时发生错误");
        }

        // 优先使用 unionid 查询，如果 unionid 为空，可以考虑是否允许仅用 openid 登录 (根据业务决定)
        String queryIdentifier = session.getUnionid();
        String identifierType = "UnionID";

        if (StringUtils.isEmpty(queryIdentifier)) {
            // 如果 unionid 为空，尝试使用 openid (如果业务允许)
             queryIdentifier = session.getOpenid();
             identifierType = "OpenID";
             log.warn("微信用户 UnionID 为空，尝试使用 OpenID 进行查询: openid={}", queryIdentifier);
             if (StringUtils.isEmpty(queryIdentifier)) {
                 throw new ServiceException("无法获取有效的用户标识 (OpenID 和 UnionID 均为空)");
             }
        }

        // 1. 根据标识符查询用户
        LambdaQueryWrapper<StSysUser> queryWrapper = new LambdaQueryWrapper<>();
        if ("UnionID".equals(identifierType)) {
             // 使用 SysUser::getUnionId 进行查询
            queryWrapper.eq(SysUser::getUnionId, queryIdentifier);
        } else {
             // 使用 SysUser::getOpenId 进行查询
            queryWrapper.eq(SysUser::getOpenId, queryIdentifier);
        }
        queryWrapper.apply("ROWNUM = 1"); // 确保只查一个
        StSysUser user = stSysUserMapper.selectOne(queryWrapper);

        if (user != null) {
            log.info("根据微信 {} [{}] 查询到本地用户: userId={}, username={}", identifierType, queryIdentifier, user.getUserId(), user.getUserName());
        } else {
             log.info("根据微信 {} [{}] 未查询到本地用户", identifierType, queryIdentifier);
        }

        // 2. 返回包含微信 session 信息和本地用户信息的组合对象
        UserInfoWithWxSession result = new UserInfoWithWxSession();
        result.setSessionInfo(session);
        result.setSysUser(user); // user 可能为 null

        return result;
    }

    /**
     * 学生端微信登录，验证用户并生成 JWT Token
     *
     * @param code 微信登录 code
     * @return JWT Token 字符串
     */
    @Override
    public StLoginVo loginAndGenerateToken(String code) {
        // 1. 调用微信接口并获取本地用户信息
        UserInfoWithWxSession userInfo = this.loginByWechatCode(code);
        StSysUser user = userInfo.getSysUser();
        String identifier = userInfo.getSessionInfo() != null ? 
                             (StringUtils.isNotEmpty(userInfo.getSessionInfo().getUnionid()) ? 
                              userInfo.getSessionInfo().getUnionid() : userInfo.getSessionInfo().getOpenid())
                              : "未知标识";
        String identifierType = StringUtils.isNotEmpty(userInfo.getSessionInfo().getUnionid()) ? "UnionID" : "OpenID";

        // 2. 检查用户是否存在
        if (user == null) {
            log.warn("微信登录失败，用户不存在: {}={}", identifierType, identifier);
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(identifier, Constants.LOGIN_FAIL, "账号不存在，请联系管理员添加账号"));
            throw new ServiceException("账号不存在，请联系管理员添加账号");
        }

        // 3. 检查用户状态
        if (UserConstants.USER_DISABLE.equals(user.getStatus())) {
            log.warn("微信登录失败，账号已停用: userId={}, username={}", user.getUserId(), user.getUserName());
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(user.getUserName(), Constants.LOGIN_FAIL, "用户已停用，请联系管理员"));
            throw new ServiceException("对不起，您的账号已被停用");
        }
        if ("2".equals(user.getDelFlag())) {
            log.warn("微信登录失败，账号已被删除: userId={}, username={}", user.getUserId(), user.getUserName());
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(user.getUserName(), Constants.LOGIN_FAIL, "用户已被删除，请联系管理员"));
            throw new ServiceException("对不起，您的账号已被删除");
        }

        // 4. 验证通过，构建 StLoginUser 用于生成 Token
        StLoginUser stLoginUser = new StLoginUser();
        stLoginUser.setUserId(user.getUserId());
        stLoginUser.setUsername(user.getUserName());
        stLoginUser.setNickname(user.getNickName());
        stLoginUser.setAvatar(user.getAvatar());
        stLoginUser.setIdentifier(identifier);

        // 5. 生成 JWT Token
        String token = StAuthUtil.createToken(stLoginUser);


        // 6. 记录登录成功日志 (异步)
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(user.getUserName(), Constants.LOGIN_SUCCESS, "学生端微信登录成功"));

        // 7. 构建并返回 StLoginVo 对象
        return new StLoginVo(token, stLoginUser);
    }

    /**
     * 查询学生课程信息
     *
     * @return 学生课程信息
     */
    @Override
    public List<StudentCourseInfoDTO> getStudentCourseInfo() {
        // 获取当前登录用户信息，未登录会抛出401异常
        StLoginUser loginUser = StAuthUtil.getRequiredLoginUser();
        log.info("[StSysUserService] 查询用户 {} 的课程信息", loginUser.getUsername());

        try {
            // 通过用户名和用户ID查询课程信息
            List<StudentCourseInfoDTO> courseInfoList = stSysUserMapper.selectStudentCourseInfo(
                    loginUser.getUsername(), loginUser.getUserId());

            // 处理马甲图标URL拼接
            if (courseInfoList != null && !courseInfoList.isEmpty()) {
                for (StudentCourseInfoDTO courseInfo : courseInfoList) {
                    processPuppetIcon(courseInfo);
                }
                log.info("[StSysUserService] 用户 {} 的课程分组信息查询完成，共 {} 条记录", 
                        loginUser.getUsername(), courseInfoList.size());
            } else {
                log.info("[StSysUserService] 用户 {} 暂无课程分组信息", loginUser.getUsername());
                courseInfoList = new ArrayList<>();
            }

            // 查询课程群信息
            List<StudentCourseInfoDTO> courseGroupList = stSysUserMapper.selectStudentCourseGroupInfo(
                    loginUser.getUsername(), loginUser.getUserId());

            if (courseGroupList != null && !courseGroupList.isEmpty()) {
                // 将课程群信息插入到结果列表的开头
                for (int i = courseGroupList.size() - 1; i >= 0; i--) {
                    StudentCourseInfoDTO courseGroup = courseGroupList.get(i);
                    courseInfoList.add(0, courseGroup);
                }
                log.info("[StSysUserService] 用户 {} 的课程群信息查询完成，共 {} 条记录", 
                        loginUser.getUsername(), courseGroupList.size());
            } else {
                log.info("[StSysUserService] 用户 {} 暂无课程群信息", loginUser.getUsername());
            }

            log.info("[StSysUserService] 用户 {} 的所有课程信息查询完成，共 {} 条记录", 
                    loginUser.getUsername(), courseInfoList.size());

            return courseInfoList;

        } catch (Exception e) {
            log.error("[StSysUserService] 查询用户 {} 的课程信息失败", loginUser.getUsername(), e);
            throw new ServiceException("查询课程信息失败: " + e.getMessage());
        }
    }

    /**
     * 处理马甲图标URL拼接
     * 
     * @param courseInfo 课程信息对象
     */
    private void processPuppetIcon(StudentCourseInfoDTO courseInfo) {
        try {
            String puppetIcon = courseInfo.getPuppetIcon();
            if (StringUtils.isNotEmpty(puppetIcon) && !StrUtil.startWith(puppetIcon, "http")) {
                // 拼接完整的马甲图标URL
                String iconUrl = puppetIconService.buildPuppetIconUrlSafely(puppetIcon);
                courseInfo.setPuppetIcon(iconUrl);
                log.debug("[StSysUserService] 马甲图标URL拼接完成: {} -> {}", puppetIcon, iconUrl);
            }
        } catch (Exception e) {
            log.error("[StSysUserService] 处理马甲图标URL失败", e);
            // 不影响主流程，继续执行
        }
    }

    // /**
    //  * 根据部门ID获取分组用户信息
    //  *
    //  * @param deptId 部门ID
    //  * @return 分组用户信息列表
    //  */
    // @Override
    // public List<GroupUserDTO> getGroupUsers(Long deptId) {
    //     if (deptId == null) {
    //         throw new ServiceException("部门ID不能为空");
    //     }
    //     List<GroupUserDTO> groupUserDTOS = stSysUserMapper.selectGroupUsersByDeptId(deptId);
    //     log.info("查询到部门 [{}] 用户 {} 人: {}", deptId, groupUserDTOS.size(), groupUserDTOS);
    //     return groupUserDTOS;
    // }

    // 内部类用于组合返回结果
    @Data
    public static class UserInfoWithWxSession {
        private WxMaJscode2SessionResult sessionInfo;
        private StSysUser sysUser;
    }

}