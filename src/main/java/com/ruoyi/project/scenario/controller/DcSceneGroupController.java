package com.ruoyi.project.scenario.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.project.scenario.domain.DcSceneQuestionItem;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.scenario.domain.DcSceneGroup;
import com.ruoyi.project.scenario.service.IDcSceneGroupService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 场景分组Controller
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@RestController
@RequestMapping("/scenario/group")
public class DcSceneGroupController extends BaseController
{
    @Autowired
    private IDcSceneGroupService dcSceneGroupService;

    /**
     * 查询场景分组列表
     */
    @PreAuthorize("@ss.hasPermi('scenario:group:list')")
    @GetMapping("/list")
    public AjaxResult list(DcSceneGroup dcSceneGroup)
    {
        LambdaQueryWrapper<DcSceneGroup> querySceneGroupWrapper = new LambdaQueryWrapper<>();
        querySceneGroupWrapper.eq(DcSceneGroup::getSceneId,dcSceneGroup.getSceneId());
        List<DcSceneGroup> list = dcSceneGroupService.list(querySceneGroupWrapper);
        return success(list);
    }

    /**
     * 获取场景分组详细信息
     */
    @GetMapping(value = "/{groupId}")
    public AjaxResult getInfo(@PathVariable("groupId") String groupId)
    {
        return success(dcSceneGroupService.getById(groupId));
    }

    /**
     * 新增场景分组
     */
    @Log(title = "场景分组", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DcSceneGroup dcSceneGroup)
    {
        dcSceneGroup.setCreateUser(getUserId().toString());
        return toAjax(dcSceneGroupService.save(dcSceneGroup));
    }

    /**
     * 修改场景分组
     */
    @Log(title = "场景分组", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DcSceneGroup dcSceneGroup)
    {
        return toAjax(dcSceneGroupService.updateById(dcSceneGroup));
    }

    /**
     * 删除场景分组
     */
    @Log(title = "场景分组", businessType = BusinessType.DELETE)
	@DeleteMapping("/{groupIds}")
    public AjaxResult remove(@PathVariable String groupIds)
    {
        return toAjax(dcSceneGroupService.removeById(groupIds));
    }
}
