package com.ruoyi.project.scenario.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.project.scenario.domain.DcCourseTestpaper;
import com.ruoyi.project.scenario.domain.DcSceneGroup;
import com.ruoyi.project.scenario.service.IDcSceneGroupService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.scenario.domain.DcScenePuppet;
import com.ruoyi.project.scenario.service.IDcScenePuppetService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 场景内的马甲Controller
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@RestController
@RequestMapping("/scenario/puppet")
public class DcScenePuppetController extends BaseController
{
    @Autowired
    private IDcScenePuppetService dcScenePuppetService;

    @Autowired
    private IDcSceneGroupService dcSceneGroupService;

    /**
     * 查询场景内的马甲列表
     */
    @GetMapping("/list")
    public AjaxResult list(DcScenePuppet dcScenePuppet)
    {
        LambdaQueryWrapper<DcScenePuppet> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcScenePuppet::getGroupId,dcScenePuppet.getGroupId());
        List<DcScenePuppet> list = dcScenePuppetService.list(queryWrapper);
        return success(list);
    }

    /**
     * 获取场景内的马甲详细信息
     */
//    @PreAuthorize("@ss.hasPermi('scenario:puppet:query')")
    @GetMapping(value = "/{puppetId}")
    public AjaxResult getInfo(@PathVariable("puppetId") String puppetId)
    {
        return success(dcScenePuppetService.getById(puppetId));
    }


    /**
     * 获取场景内的马甲详细信息
     */
    @GetMapping(value = "/puppet-group/{sceneId}")
    public AjaxResult getPuppetWithGroupBySceneId(@PathVariable("sceneId") String sceneId)
    {
        List<DcSceneGroup> list = new ArrayList<>();
        LambdaQueryWrapper<DcSceneGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcSceneGroup::getSceneId,sceneId);
        queryWrapper.orderByAsc(DcSceneGroup::getGroupOrder);
        List<DcSceneGroup> groupList = dcSceneGroupService.list(queryWrapper);
        for(DcSceneGroup gruop : groupList){
            LambdaQueryWrapper<DcScenePuppet> queryDcScenePuppetWrapper = new LambdaQueryWrapper<>();
            queryDcScenePuppetWrapper.eq(DcScenePuppet::getSceneId,sceneId);
            queryDcScenePuppetWrapper.eq(DcScenePuppet::getGroupId,gruop.getGroupId());
            queryDcScenePuppetWrapper.orderByAsc(DcScenePuppet::getPuppetIndex);
            gruop.setDcScenePuppetList(dcScenePuppetService.list(queryDcScenePuppetWrapper));
            list.add(gruop);
        }
        return success(list);
    }

    /**
     * 新增场景内的马甲
     */
    @Log(title = "场景内的马甲", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DcScenePuppet dcScenePuppet)
    {
        return toAjax(dcScenePuppetService.save(dcScenePuppet));
    }

    /**
     * 修改场景内的马甲
     */
    @Log(title = "场景内的马甲", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DcScenePuppet dcScenePuppet)
    {
        return toAjax(dcScenePuppetService.updateById(dcScenePuppet));
    }

    /**
     * 删除场景内的马甲
     */
    @Log(title = "场景内的马甲", businessType = BusinessType.DELETE)
	@DeleteMapping("/{puppetIds}")
    public AjaxResult remove(@PathVariable String puppetIds)
    {
        return toAjax(dcScenePuppetService.removeById(puppetIds));
    }
}
