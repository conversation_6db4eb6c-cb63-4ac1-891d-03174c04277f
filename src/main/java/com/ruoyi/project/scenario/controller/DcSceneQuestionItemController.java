package com.ruoyi.project.scenario.controller;

import java.io.IOException;
import java.nio.file.Paths;
import java.util.List;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.exception.file.FileNameLengthLimitExceededException;
import com.ruoyi.common.exception.file.FileSizeLimitExceededException;
import com.ruoyi.common.exception.file.InvalidExtensionException;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.framework.config.RuoYiConfig;
import com.ruoyi.project.scenario.domain.DcCourseQuestion;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.scenario.domain.DcSceneQuestionItem;
import com.ruoyi.project.scenario.service.IDcSceneQuestionItemService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 试题选项Controller
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@RestController
@RequestMapping("/scenario/item")
public class DcSceneQuestionItemController extends BaseController
{
    @Autowired
    private IDcSceneQuestionItemService dcSceneQuestionItemService;

    /**
     * 查询试题选项列表
     */
    @GetMapping("/list")
    public AjaxResult list(DcSceneQuestionItem dcSceneQuestionItem)
    {
        LambdaQueryWrapper<DcSceneQuestionItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcSceneQuestionItem::getQuestionId,dcSceneQuestionItem.getQuestionId());
        List<DcSceneQuestionItem> list = dcSceneQuestionItemService.list(queryWrapper);
        return success(list);
    }

    /**
     * 获取试题选项详细信息
     */
    @GetMapping(value = "/{itemId}")
    public AjaxResult getInfo(@PathVariable("itemId") String itemId)
    {
        return success(dcSceneQuestionItemService.getById(itemId));
    }

    /**
     * 新增试题选项
     */
    @Log(title = "试题选项", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DcSceneQuestionItem dcSceneQuestionItem)
    {
        dcSceneQuestionItem.setCreateUser(getUserId().toString());
        dcSceneQuestionItem.setItemId(UUID.fastUUID().toString());
        dcSceneQuestionItem.setIsRight("N");
        return toAjax(dcSceneQuestionItemService.save(dcSceneQuestionItem));
    }

    /**
     * 修改试题选项
     */
    @Log(title = "试题选项", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DcSceneQuestionItem dcSceneQuestionItem)
    {
        dcSceneQuestionItem.setModifyUser(getUserId().toString());
        return toAjax(dcSceneQuestionItemService.updateById(dcSceneQuestionItem));
    }

    @Log(title = "试题选项", businessType = BusinessType.UPDATE)
    @PutMapping("/change-select/{itemId}")
    public AjaxResult changeSelect(@PathVariable String itemId)
    {
        return toAjax(dcSceneQuestionItemService.updateDcSceneQuestionItemIsRight(itemId));
    }

    /**
     * 删除试题选项
     */
    @Log(title = "试题选项", businessType = BusinessType.DELETE)
	@DeleteMapping("/{itemIds}")
    public AjaxResult remove(@PathVariable String itemIds)
    {
        return toAjax(dcSceneQuestionItemService.removeById(itemIds));
    }

}
