package com.ruoyi.project.scenario.controller;

import java.util.List;
import java.util.UUID;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.project.scenario.domain.DcSceneQuestionItem;
import com.ruoyi.project.scenario.domain.DcSceneStageAnnex;
import com.ruoyi.project.scenario.service.IDcSceneStageAnnexService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.scenario.domain.DcSceneStage;
import com.ruoyi.project.scenario.service.IDcSceneStageService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 场景阶段Controller
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@RestController
@RequestMapping("/scenario/stage")
public class DcSceneStageController extends BaseController
{
    @Autowired
    private IDcSceneStageService dcSceneStageService;
    @Autowired
    private IDcSceneStageAnnexService dcSceneStageAnnexService;

    /**
     * 查询场景阶段列表
     */
    @GetMapping("/list")
    public AjaxResult list(DcSceneStage dcSceneStage)
    {
        LambdaQueryWrapper<DcSceneStage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcSceneStage::getSceneId,dcSceneStage.getSceneId());
        queryWrapper.orderByAsc(DcSceneStage::getSceneStageOrder);
        List<DcSceneStage> list = dcSceneStageService.list(queryWrapper);
        return success(list);
    }

    /**
     * 获取场景阶段详细信息
     */
    @GetMapping(value = "/{sceneStageId}")
    public AjaxResult getInfo(@PathVariable("sceneStageId") String sceneStageId)
    {
        DcSceneStage dcSceneStage = dcSceneStageService.getById(sceneStageId);
        return success(dcSceneStage);
    }

    /**
     * 新增场景阶段
     */
    @Log(title = "场景阶段", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DcSceneStage dcSceneStage)
    {
        String id = UUID.randomUUID().toString();
        dcSceneStage.setSceneStageId(id);
        dcSceneStage.setCreateUser(getUserId().toString());
        return toAjax(dcSceneStageService.save(dcSceneStage));
    }

    /**
     * 修改场景阶段
     */
    @PreAuthorize("@ss.hasPermi('scenario:stage:edit')")
    @Log(title = "场景阶段", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DcSceneStage dcSceneStage)
    {
        dcSceneStage.setModifyUser(getUserId().toString());
        return toAjax(dcSceneStageService.updateById(dcSceneStage));
    }

    /**
     * 删除场景阶段
     */
//    @PreAuthorize("@ss.hasPermi('scenario:stage:remove')")
    @Log(title = "场景阶段", businessType = BusinessType.DELETE)
	@DeleteMapping("/{sceneStageIds}")
    public AjaxResult remove(@PathVariable String sceneStageIds)
    {
        return toAjax(dcSceneStageService.removeById(sceneStageIds));
    }
}
