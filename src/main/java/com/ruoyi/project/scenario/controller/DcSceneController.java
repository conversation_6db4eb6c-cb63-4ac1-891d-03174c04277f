package com.ruoyi.project.scenario.controller;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.project.scenario.domain.DcSceneQuestionItem;
import com.ruoyi.project.system.domain.SysRole;
import com.ruoyi.project.system.domain.SysUser;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.scenario.domain.DcScene;
import com.ruoyi.project.scenario.service.IDcSceneService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 场景Controller
 * 
 * <AUTHOR>
 * @date 2025-05-05
 */
@RestController
@RequestMapping("/scenario/scene")
public class DcSceneController extends BaseController
{
    @Autowired
    private IDcSceneService dcSceneService;

    /**
     * 查询场景列表
     */
    @GetMapping("/list")
    public AjaxResult list(DcScene dcScene)
    {
        LambdaQueryWrapper<DcScene> queryDcSceneWrapper = new LambdaQueryWrapper<>();
        queryDcSceneWrapper.eq(DcScene::getCreateUser,getUserId().toString());
        if(dcScene.getSceneTagid() != null ){
            queryDcSceneWrapper.eq(DcScene::getSceneTagid,dcScene.getSceneTagid());
        }
        queryDcSceneWrapper.ne(DcScene::getIsdelete,"1");
        return success(dcSceneService.list(queryDcSceneWrapper));
    }

    @PreAuthorize("@ss.hasPermi('scenario:scene:list')")
    @GetMapping("/tagslist")
    public List<Map<String,Object>> tagslist(Map<String,Object> map)
    {
        map.put("userId",getUserId());
        List<Map<String,Object>> list = dcSceneService.selectGetTagsByUser(map);
        return list;
    }


    /**
     * 获取场景详细信息
     */
    @GetMapping(value = "/{sceneId}")
    public AjaxResult getInfo(@PathVariable("sceneId") String sceneId)
    {
        return success(dcSceneService.getById(sceneId));
    }

    /**
     * 新增场景
     */
//    @PreAuthorize("@ss.hasPermi('scenario:scene:add')")
    @Log(title = "场景", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DcScene dcScene)
    {
        dcScene.setCreateUser(getUserId().toString());
        dcScene.setSceneUserIdnumber(getUserId());
        dcScene.setIsdelete("0");
        return toAjax(dcSceneService.save(dcScene));
    }

    /**
     * 新增场景分类
     * @param list
     * @return
     */
//    @PreAuthorize("@ss.hasPermi('scenario:scene:add')")
    @Log(title = "场景", businessType = BusinessType.INSERT)
    @PostMapping("/edittag")
    public AjaxResult editTag(@RequestBody List<Map<String,Object>> list)
    {
        for(Map<String,Object> map : list){
            String tagId = map.get("TAG_ID")+"";
            if("".equals(tagId)){
                map.put("TAG_ID", UUID.fastUUID().toString());
                map.put("CREATE_USER",getUserId().toString());
                dcSceneService.addTag(map);
            }else{
                map.put("MODIFY_USER",getUserId().toString());
                dcSceneService.updateTag(map);
            }
        }

        return AjaxResult.success();
    }

    /**
     * 修改场景
     */
//    @PreAuthorize("@ss.hasPermi('scenario:scene:edit')")
    @Log(title = "场景", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DcScene dcScene)
    {
        dcScene.setModifyUser(getUserId().toString());
        return toAjax(dcSceneService.updateById(dcScene));
    }

    /**
     * 删除场景
     */
    @Log(title = "场景", businessType = BusinessType.DELETE)
	@DeleteMapping("/{sceneId}")
    public AjaxResult remove(@PathVariable String sceneId)
    {
        return toAjax(dcSceneService.removeById(sceneId));
    }

    @Log(title = "场景", businessType = BusinessType.DELETE)
    @DeleteMapping("/tag/{tagid}")
    public AjaxResult delTag(@PathVariable String tagid)
    {
        return toAjax(dcSceneService.delTag(tagid));
    }

    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @GetMapping("/gruop/{sceneId}")
    public AjaxResult gruop(@PathVariable("sceneId") String sceneId)
    {
        AjaxResult ajax = AjaxResult.success();
//        SysUser user = userService.selectUserById(userId);
//        List<SysRole> roles = roleService.selectRolesByUserId(userId);
//        ajax.put("user", user);
//        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return ajax;
    }

//    @PreAuthorize("@ss.hasPermi('scenario:scene:list')")
    @GetMapping("/puppet/ico")
    public List<Map<String,String>> icoList(DcScene dcScene)
    {
        String folderPath = "src\\main\\resources\\static\\puppet";
        List<Map<String,String>> list = new ArrayList<>();
        File folder = new File(folderPath);
        File[] listOfFiles = folder.listFiles();

        if (listOfFiles != null) {
            for (File file : listOfFiles) {
                if (file.isFile() && file.getName().toLowerCase().endsWith(".png")) {
                    Map<String,String> map = new HashMap<>();
                    map.put("name",file.getName());
                    list.add(map);
                }
            }
        }
        return list;
    }
}
