package com.ruoyi.project.scenario.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.framework.config.RuoYiConfig;
import com.ruoyi.framework.config.ServerConfig;
import com.ruoyi.project.scenario.domain.DcCourseTestpaper;
import com.ruoyi.project.scenario.domain.DcSceneQuestionItem;
import com.ruoyi.project.scenario.service.IDcSceneQuestionItemService;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.scenario.domain.DcSceneQuestion;
import com.ruoyi.project.scenario.service.IDcSceneQuestionService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 试题Controller
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@RestController
@RequestMapping("/scenario/question")
public class DcSceneQuestionController extends BaseController
{
    @Autowired
    private IDcSceneQuestionService dcSceneQuestionService;

    @Autowired
    private IDcSceneQuestionItemService dcSceneQuestionItemService;

    @Autowired
    private ServerConfig serverConfig;

    /**
     * 查询试题列表
     */
    @GetMapping("/list")
    public AjaxResult list(DcSceneQuestion dcSceneQuestion)
    {
        LambdaQueryWrapper<DcSceneQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcSceneQuestion::getPaperId,dcSceneQuestion.getPaperId());
        queryWrapper.orderByAsc(DcSceneQuestion::getQuestionIndex);
        List<DcSceneQuestion> list = dcSceneQuestionService.list(queryWrapper);
        for(DcSceneQuestion question : list){
            LambdaQueryWrapper<DcSceneQuestionItem> queryDcSceneQuestionItemWrapper = new LambdaQueryWrapper<>();
            queryDcSceneQuestionItemWrapper.eq(DcSceneQuestionItem::getQuestionId,question.getQuestionId());
            queryDcSceneQuestionItemWrapper.orderByAsc(DcSceneQuestionItem::getItemIndex);
            List<DcSceneQuestionItem> questionItemList = dcSceneQuestionItemService.list(queryDcSceneQuestionItemWrapper);
            question.setQuestionItem(questionItemList);
        }
        return success(list);
    }


    /**
     * 获取试题详细信息
     */
    @GetMapping(value = "/{questionId}")
    public AjaxResult getInfo(@PathVariable("questionId") String questionId)
    {
        return success(dcSceneQuestionService.getById(questionId));
    }

    /**
     * 新增试题
     */
    @Log(title = "试题", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DcSceneQuestion dcSceneQuestion)
    {
        String questionId = UUID.fastUUID().toString();
        dcSceneQuestion.setQuestionId(questionId);
        dcSceneQuestion.setCreateUser(getUserId().toString());
        DcSceneQuestionItem dcSceneQuestionItem = new DcSceneQuestionItem();
        dcSceneQuestionItem.setCreateUser(getUserId().toString());
        dcSceneQuestionItem.setItemId(UUID.fastUUID().toString());
        dcSceneQuestionItem.setQuestionId(questionId);
        dcSceneQuestionItem.setItemIndex(0);
        dcSceneQuestionItemService.save(dcSceneQuestionItem);
        return success(dcSceneQuestionService.save(dcSceneQuestion));
    }

    /**
     * 修改试题
     */
    @Log(title = "试题", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DcSceneQuestion dcSceneQuestion)
    {
        dcSceneQuestion.setModifyUser(getUserId().toString());
        return toAjax(dcSceneQuestionService.updateById(dcSceneQuestion));
    }

    @Log(title = "试题", businessType = BusinessType.UPDATE)
    @PutMapping("move")
    public AjaxResult move(@RequestBody Map<String,Object> map)
    {
        String type = map.get("type")+"";
        String questionId = map.get("questionId")+"";
        DcSceneQuestion question = dcSceneQuestionService.getById(questionId);
        DcSceneQuestion questionSiblings = null;
        if("1".equals(type)){
            questionSiblings = dcSceneQuestionService.getLagQuestion(questionId,question.getPaperId());
        }else if("2".equals(type)){
            questionSiblings = dcSceneQuestionService.getLeadQuestion(questionId,question.getPaperId());
        }
        Long index = questionSiblings.getQuestionIndex();
        questionSiblings.setQuestionIndex(question.getQuestionIndex());
        question.setQuestionIndex(index);
        dcSceneQuestionService.updateById(questionSiblings);
        return success(dcSceneQuestionService.updateById(question));
    }

    /**
     * 删除试题
     */
    @Log(title = "试题", businessType = BusinessType.DELETE)
	@DeleteMapping("/{questionIds}")
    public AjaxResult remove(@PathVariable String questionIds)
    {
        return toAjax(dcSceneQuestionService.removeById(questionIds));
    }

    @PostMapping("/upload")
    public AjaxResult uploadFile(MultipartFile file, HttpServletRequest request) throws Exception
    {
        try
        {
            String paperid = request.getHeader("Paperid");
            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("paperid", paperid);

            String excelFilePath = filePath + fileName.replace("/profile/upload",""); // 或者 ".xls" 对于老版本的Excel文件

            try (FileInputStream fis = new FileInputStream(new File(excelFilePath));
                 Workbook workbook = isXLSX(excelFilePath) ? new XSSFWorkbook(fis) : new HSSFWorkbook(fis)) {
                Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表
                String createUser = getUserId().toString();
                int rowIndex = 1;
                for (Row row : sheet) {
                    if(rowIndex > 1){
                        int cellIndex = 1;
                        DcSceneQuestion dcSceneQuestion = new DcSceneQuestion();
                        dcSceneQuestion.setPaperId(paperid);
                        String questionId = UUID.fastUUID().toString();
                        dcSceneQuestion.setQuestionId(questionId);
                        dcSceneQuestion.setCreateUser(createUser);
                        dcSceneQuestion.setCreateTime(DateUtils.getNowDate());
                        for (Cell cell : row) {
                            String value = cell.toString();
                            if(cellIndex == 1){
                                if(value.indexOf(".")>0){
                                    value = value.substring(0,value.indexOf("."));
                                }
                                dcSceneQuestion.setQuestionIndex(Long.valueOf(value));
                            }else if(cellIndex ==2){
                                String type = "2";
                                if("单选".equals(value)){
                                    type = "1";
                                }
                                dcSceneQuestion.setQuestionType(type);
                            }else if(cellIndex ==3){
                                dcSceneQuestion.setQuestonText(value);
                            }else{
                                DcSceneQuestionItem dcSceneQuestionItem = new DcSceneQuestionItem();
                                dcSceneQuestionItem.setItemId(UUID.fastUUID().toString());
                                dcSceneQuestionItem.setQuestionId(questionId);
                                dcSceneQuestionItem.setItemText(value);
                                dcSceneQuestionItem.setCreateUser(createUser);
                                dcSceneQuestionItem.setItemIndex(cellIndex-3);
                                dcSceneQuestionItemService.save(dcSceneQuestionItem);
                            }
                            cellIndex++;
                        }
                        dcSceneQuestionService.save(dcSceneQuestion);
                    }
                    rowIndex++;
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }
    private static boolean isXLSX(String filePath) {
        return filePath.toLowerCase().endsWith(".xlsx");
    }
}
