package com.ruoyi.project.scenario.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.project.scenario.domain.DcSceneQuestionItem;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.scenario.domain.DcSceneTestpaper;
import com.ruoyi.project.scenario.service.IDcSceneTestpaperService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 问卷调查Controller
 * 
 * <AUTHOR>
 * @date 2025-05-26
 */
@RestController
@RequestMapping("/scenario/testpaper")
public class DcSceneTestpaperController extends BaseController
{
    @Autowired
    private IDcSceneTestpaperService dcSceneTestpaperService;

    /**
     * 查询问卷调查列表
     */
    @GetMapping("/list")
    public AjaxResult list(DcSceneTestpaper dcSceneTestpaper)
    {
        LambdaQueryWrapper<DcSceneTestpaper> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DcSceneTestpaper::getSceneId,dcSceneTestpaper.getSceneId());
        List<DcSceneTestpaper> list = dcSceneTestpaperService.list(queryWrapper);
        return success(list);
    }

    /**
     * 获取问卷调查详细信息
     */
    @GetMapping(value = "/{paperId}")
    public AjaxResult getInfo(@PathVariable("paperId") String paperId)
    {
        return success(dcSceneTestpaperService.getById(paperId));
    }

    /**
     * 新增问卷调查
     */
    @Log(title = "问卷调查", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DcSceneTestpaper dcSceneTestpaper)
    {
        dcSceneTestpaper.setCreateUser(getUserId().toString());
        return toAjax(dcSceneTestpaperService.save(dcSceneTestpaper));
    }

    /**
     * 修改问卷调查
     */
    @Log(title = "问卷调查", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DcSceneTestpaper dcSceneTestpaper)
    {
        dcSceneTestpaper.setModifyUser(getUsername());
        return toAjax(dcSceneTestpaperService.updateById(dcSceneTestpaper));
    }

    /**
     * 删除问卷调查
     */
    @Log(title = "问卷调查", businessType = BusinessType.DELETE)
	@DeleteMapping("/{paperIds}")
    public AjaxResult remove(@PathVariable String paperIds)
    {
        return toAjax(dcSceneTestpaperService.removeById(paperIds));
    }
}
