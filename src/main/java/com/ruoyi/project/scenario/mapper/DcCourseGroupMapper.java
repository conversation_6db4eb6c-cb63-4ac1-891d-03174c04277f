package com.ruoyi.project.scenario.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.scenario.domain.DcCourseGroup;
import com.ruoyi.project.scenario.domain.DcSceneGroup;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 课程分组Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface DcCourseGroupMapper extends BaseMapper<DcCourseGroup> {
    public List<Map<String, Object>> getTeGroupList(String courseId);

    public List<Map<String, Object>> getStudentListByGroup(Map<String, Object> getMap);

    public List<Map<String, Object>> getListByCourseId(String courseId);
} 