package com.ruoyi.project.scenario.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.scenario.domain.DcCourse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.xmlbeans.impl.xb.xsdschema.Public;

import java.util.List;
import java.util.Map;

/**
 * 课程Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface DcCourseMapper extends BaseMapper<DcCourse> {
    public List<DcCourse> selectCourseList(Map<String,Object> map);


    public List<DcCourse> getClassList();

    public Map<String, Object> checkRandomString(String randomString);

    public List<Map<String, Object>> selectGetTagsByUser(Map<String, Object> map);

    public DcCourse selectDcCourseByCourseId(String courseId);

    public List<DcCourse> selectDcCourseList(Map<String, Object> getCourseMap);
}