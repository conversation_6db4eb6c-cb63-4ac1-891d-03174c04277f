package com.ruoyi.project.scenario.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.project.scenario.domain.DcCourseStudent;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 课程学生Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface DcCourseStudentMapper extends BaseMapper<DcCourseStudent> {
    public List<DcCourseStudent> getStudentList(String puppetId);
    public int updatePlusIndex(DcCourseStudent dcCourseStudent);
    public int updateMinusIndex(DcCourseStudent dcCourseStudent);

    public List<Map<String, Object>> listByPuppetId(String puppetId);
}