package com.ruoyi.project.scenario.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.project.scenario.domain.DcCourseChatHistory;
import com.ruoyi.project.scenario.mapper.DcCourseChatHistoryMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.scenario.mapper.DcScenePuppetMapper;
import com.ruoyi.project.scenario.domain.DcScenePuppet;
import com.ruoyi.project.scenario.service.IDcScenePuppetService;

/**
 * 场景内的马甲Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Service
public class DcScenePuppetServiceImpl extends ServiceImpl<DcScenePuppetMapper, DcScenePuppet> implements IDcScenePuppetService
{

}
