package com.ruoyi.project.scenario.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.project.scenario.domain.DcCourseTestpaper;
import com.ruoyi.project.scenario.mapper.DcCourseTestpaperMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.scenario.mapper.DcSceneGroupMapper;
import com.ruoyi.project.scenario.domain.DcSceneGroup;
import com.ruoyi.project.scenario.service.IDcSceneGroupService;

/**
 * 场景分组Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Service
public class DcSceneGroupServiceImpl extends ServiceImpl<DcSceneGroupMapper, DcSceneGroup> implements IDcSceneGroupService
{

}
