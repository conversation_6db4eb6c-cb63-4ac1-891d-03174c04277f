package com.ruoyi.project.scenario.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.project.scenario.domain.DcSceneQuestionItem;
import com.ruoyi.project.scenario.mapper.DcSceneQuestionItemMapper;
import com.ruoyi.project.scenario.service.IDcSceneQuestionItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 试题选项Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@Service
public class DcSceneQuestionItemServiceImpl extends ServiceImpl<DcSceneQuestionItemMapper, DcSceneQuestionItem> implements IDcSceneQuestionItemService
{
    @Autowired
    private DcSceneQuestionItemMapper dcSceneQuestionItemMapper;

    @Override
    public int updateDcSceneQuestionItemIsRight(String itemId) {
        return dcSceneQuestionItemMapper.updateDcSceneQuestionItemIsRight(itemId);
    }
}
