package com.ruoyi.project.scenario.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.project.scenario.domain.DcSceneQuestion;
import com.ruoyi.project.scenario.mapper.DcCourseStudentMapper;
import com.ruoyi.project.scenario.mapper.DcSceneQuestionMapper;
import com.ruoyi.project.scenario.service.IDcSceneQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 试题Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Service
public class DcSceneQuestionServiceImpl extends ServiceImpl< DcSceneQuestionMapper,  DcSceneQuestion> implements IDcSceneQuestionService
{

    @Autowired
    private DcSceneQuestionMapper dcSceneQuestionMapper;

    @Override
    public DcSceneQuestion getLagQuestion(String questionId, String paperId) {
        return dcSceneQuestionMapper.getLagQuestion(questionId,paperId);
    }

    @Override
    public DcSceneQuestion getLeadQuestion(String questionId, String paperId) {
        return dcSceneQuestionMapper.getLeadQuestion(questionId,paperId);
    }
}
