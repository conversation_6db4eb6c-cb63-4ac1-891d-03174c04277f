package com.ruoyi.project.scenario.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.project.scenario.domain.DcSceneStage;
import com.ruoyi.project.scenario.mapper.DcSceneStageMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.scenario.mapper.DcSceneTestpaperMapper;
import com.ruoyi.project.scenario.domain.DcSceneTestpaper;
import com.ruoyi.project.scenario.service.IDcSceneTestpaperService;

/**
 * 问卷调查Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-26
 */
@Service
public class DcSceneTestpaperServiceImpl  extends ServiceImpl<DcSceneTestpaperMapper, DcSceneTestpaper> implements IDcSceneTestpaperService
{

}
