package com.ruoyi.project.scenario.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.project.scenario.domain.DcScene;

import java.util.List;
import java.util.Map;

/**
 * 场景Service接口
 * 
 * <AUTHOR>
 * @date 2025-05-05
 */
public interface IDcSceneService extends IService<DcScene>
{

    public List<Map<String, Object>> selectGetTagsByUser(Map<String, Object> map);

    public int addTag(Map<String, Object> map);

    public int updateTag(Map<String, Object> map);

    public int delTag(String tagid);
}
