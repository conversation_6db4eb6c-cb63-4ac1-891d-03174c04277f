package com.ruoyi.project.scenario.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.project.scenario.domain.DcSceneStageAnnex;
import com.ruoyi.project.scenario.mapper.DcSceneStageAnnexMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.scenario.mapper.DcSceneStageMapper;
import com.ruoyi.project.scenario.domain.DcSceneStage;
import com.ruoyi.project.scenario.service.IDcSceneStageService;

/**
 * 场景阶段Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Service
public class DcSceneStageServiceImpl extends ServiceImpl<DcSceneStageMapper, DcSceneStage> implements IDcSceneStageService
{

}
