package com.ruoyi.project.scenario.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.project.scenario.domain.DcScene;
import com.ruoyi.project.scenario.mapper.DcSceneMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.scenario.mapper.DcSceneStageAnnexMapper;
import com.ruoyi.project.scenario.domain.DcSceneStageAnnex;
import com.ruoyi.project.scenario.service.IDcSceneStageAnnexService;

/**
 * 阶段附件，例如PDF或者视频等Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Service
public class DcSceneStageAnnexServiceImpl extends ServiceImpl<DcSceneStageAnnexMapper, DcSceneStageAnnex> implements IDcSceneStageAnnexService
{

}
