package com.ruoyi.project.scenario.domain;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 场景分组对象 dc_scene_group
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
@TableName("DC_SCENE_GROUP")
public class DcSceneGroup implements Serializable
{
    private static final long serialVersionUID = 1L;

    @TableId(value = "GROUP_ID", type = IdType.ASSIGN_ID)
    @Excel(name = "groupId")
    private String groupId;

    /** $column.columnComment */
    @TableField(value = "SCENE_ID")
    @Excel(name = "SCENE_ID")
    private String sceneId;

    /** $column.columnComment */
    @Excel(name = "GROUP_NAME")
    @TableField(value = "GROUP_NAME")
    private String groupName;

    /** $column.columnComment */
    @Excel(name = "GROUP_NUM")
    @TableField(value = "GROUP_NUM")
    private String groupNum;

    /** $column.columnComment */
    @Excel(name = "GROUP_ORDER")
    @TableField(value = "GROUP_ORDER")
    private Integer groupOrder;

    /** $column.columnComment */
    @Excel(name = "GROUP_ICON")
    @TableField(value = "GROUP_ICON")
    private String groupIcon;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "CREATE_TIME")
    private Date createTime;

    /** 创建人 */
    @Excel(name = "创建人")
    @TableField(value = "CREATE_USER")
    private String createUser;

    /** 修改人 */
    @Excel(name = "修改人")
    @TableField(value = "MODIFY_USER")
    private String modifyUser;


    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "MODIFY_TIME")
    private Date modifyTime;

    @TableField(exist = false)
    private List<DcScenePuppet> DcScenePuppetList;


}
