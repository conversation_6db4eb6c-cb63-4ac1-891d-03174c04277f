package com.ruoyi.project.scenario.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 阶段附件，例如PDF或者视频等对象 dc_scene_stage_annex
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
@TableName("DC_SCENE_STAGE_ANNEX")
public class DcSceneStageAnnex implements Serializable
{
    private static final long serialVersionUID = 1L;


}
