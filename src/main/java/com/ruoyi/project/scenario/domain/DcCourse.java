package com.ruoyi.project.scenario.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 课程实体类
 * 对应表：DC_COURSE
 * 
 * <AUTHOR>
 */
@Data
@TableName("DC_COURSE")
public class DcCourse implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 课程ID
     */
    @TableId(value = "COURSE_ID", type = IdType.ASSIGN_ID)
    private String courseId;

    /**
     * 课程名称
     */
    @TableField("COURSE_NAME")
    private String courseName;

    /**
     * 课程介绍
     */
    @TableField("COURSE_INTRODUCTION")
    private String courseIntroduction;

    /**
     * 课程创建者用户ID
     */
    @TableField("COURSE_USER_IDNUMBER")
    private String courseUserIdnumber;

    /**
     * 创建人
     */
    @TableField("CREATE_USER")
    private String createUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "CREATE_TIME")
    @Excel(name = "createTime")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("MODIFY_USER")
    private String modifyUser;

    /**
     * 修改时间
     */
    @TableField("MODIFY_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    /**
     * 场景ID
     */
    @TableField("SCENE_ID")
    private String sceneId;

    /**
     * 是否删除（0-未删除，1-已删除）
     */
    @TableField("ISDELETE")
    private Integer isDelete;

    /**
     * 班级代码
     */
    @TableField("CLASS_CODE")
    private String classCode;

    @TableField("COURSE_CODE")
    private String courseCode;

    @TableField(exist = false)
    private String sceneImage;
} 