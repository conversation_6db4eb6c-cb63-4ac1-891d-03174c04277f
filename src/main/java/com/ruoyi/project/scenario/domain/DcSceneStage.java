package com.ruoyi.project.scenario.domain;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 场景阶段对象 dc_scene_stage
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Data
@TableName("DC_SCENE_STAGE")
public class DcSceneStage implements Serializable
{
    private static final long serialVersionUID = 1L;

    @Excel(name = "sceneStageId")
    @TableId(value = "SCENE_STAGE_ID",type = IdType.ASSIGN_ID)
    private String sceneStageId;

    /** 外键场景ID */
    @Excel(name = "外键场景ID")
    @TableField(value = "SCENE_ID")
    private String sceneId;

    /** $column.columnComment */
    @Excel(name = "sceneStageTitle")
    @TableField(value = "SCENE_STAGE_TITLE")
    private String sceneStageTitle;

    /** $column.columnComment */
    @Excel(name = "sceneStageText")
    @TableField(value = "SCENE_STAGE_TEXT")
    private String sceneStageText;

    /** $column.columnComment */
    @Excel(name = "sceneStageOrder")
    @TableField(value = "SCENE_STAGE_ORDER")
    private Integer sceneStageOrder;

    /** 创建人 */
    @Excel(name = "创建人")
    @TableField(value = "CREATE_USER")
    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "CREATE_TIME")
    @Excel(name = "createTime")
    private Date createTime;

    /** 修改人 */
    @Excel(name = "修改人")
    @TableField(value = "MODIFY_USER")
    private String modifyUser;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "MODIFY_TIME")
    private Date modifyTime;
}
