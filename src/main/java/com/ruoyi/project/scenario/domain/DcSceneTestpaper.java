package com.ruoyi.project.scenario.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 问卷调查对象 dc_scene_testpaper
 * 
 * <AUTHOR>
 * @date 2025-05-26
 */
@Data
@TableName("DC_SCENE_TESTPAPER")
public class DcSceneTestpaper implements Serializable
{
    private static final long serialVersionUID = 1L;

    @Excel(name = "paperId")
    @TableId(value = "PAPER_ID",type = IdType.ASSIGN_ID)
    private String paperId;

    @Excel(name = "paperTitle")
    @TableField(value = "PAPER_TITLE")
    private String paperTitle;

    @Excel(name = "sceneId")
    @TableField(value = "SCENE_ID")
    private String sceneId;

    /** 创建人 */
    @Excel(name = "创建人")
    @TableField(value = "CREATE_USER")
    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "CREATE_TIME")
    @Excel(name = "createTime")
    private Date createTime;

    /** 修改人 */
    @Excel(name = "修改人")
    @TableField(value = "MODIFY_USER")
    private String modifyUser;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "MODIFY_TIME")
    private Date modifyTime;
}
