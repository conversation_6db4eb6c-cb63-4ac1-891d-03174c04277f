package com.ruoyi.project.scenario.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 试题选项对象 dc_scene_question_item
 * 
 * <AUTHOR>
 * @date 2025-05-27
 */
@Data
@TableName("DC_SCENE_QUESTION_ITEM")
public class DcSceneQuestionItem implements Serializable
{
    private static final long serialVersionUID = 1L;
    @TableId(value = "ITEM_ID", type = IdType.ASSIGN_ID)
    @Excel(name = "itemId")
    private String itemId;

    /** $column.columnComment */
    @TableField(value = "QUESTION_ID")
    @Excel(name = "questionId")
    private String questionId;

    /** $column.columnComment */
    @TableField(value = "ITEM_TEXT")
    @Excel(name = "itemText")
    private String itemText;

    /** 1是单选 2是多选 */
    @Excel(name = "1是单选 2是多选")
    @TableField(value = "IS_RIGHT")
    private String isRight;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "MODIFY_TIME")
    private Date modifyTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "CREATE_TIME")
    @Excel(name = "createTime")
    private Date createTime;

    /** 创建人 */
    @Excel(name = "创建人")
    @TableField(value = "CREATE_USER")
    private String createUser;

    /** 修改人 */
    @Excel(name = "修改人")
    @TableField(value = "MODIFY_USER")
    private String modifyUser;

    @TableField(value = "ITEM_INDEX")
    private Integer itemIndex;
}
