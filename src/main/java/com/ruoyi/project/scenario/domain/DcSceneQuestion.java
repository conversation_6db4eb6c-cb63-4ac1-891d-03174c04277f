package com.ruoyi.project.scenario.domain;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 试题对象 dc_scene_question
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
@TableName("DC_SCENE_QUESTION")
public class DcSceneQuestion<Int> implements Serializable
{
    private static final long serialVersionUID = 1L;

    @TableId(value = "QUESTION_ID", type = IdType.ASSIGN_ID)
    @Excel(name = "questionId")
    private String questionId;

    @TableField(value = "QUESTON_TEXT")
    @Excel(name = "questonText")
    private String questonText;

    /** 1是单选 2是多选 */
    @TableField(value = "QUESTION_TYPE")
    @Excel(name = "1是单选 2是多选")
    private String questionType;

    @TableField(value = "PAPER_ID")
    @Excel(name = "paperId")
    private String paperId;

    @TableField(value = "QUESTION_ANSWER")
    @Excel(name = "questionAnswer")
    private String questionAnswer;

    @TableField(value = "QUESTION_POINT")
    @Excel(name = "questionPoint")
    private Long questionPoint;

    /** 创建人 */
    @Excel(name = "创建人")
    @TableField(value = "CREATE_USER")
    private String createUser;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "CREATE_TIME")
    private Date createTime;

    /** 修改人 */
    @Excel(name = "修改人")
    @TableField(value = "MODIFY_USER")
    private String modifyUser;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    @TableField(value = "MODIFY_TIME")
    private Date modifyTime;

    @TableField(exist = false)
    private List<DcSceneQuestionItem> questionItem;

    @TableField(value = "QUESTION_INDEX")
    private Long questionIndex;
}
