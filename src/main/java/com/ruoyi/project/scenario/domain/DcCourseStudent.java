package com.ruoyi.project.scenario.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 课程学生实体类
 * 对应表：DC_COURSE_STUDENT
 * 
 * <AUTHOR>
 */
@Data
@TableName("DC_COURSE_STUDENT")
public class DcCourseStudent implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 学生ID
     */
    @TableId(value = "STUDENT_ID", type = IdType.ASSIGN_ID)
    private String studentId;

    /**
     * 学生代码
     */
    @TableField("STUDENT_CODE")
    private String studentCode;

//    /**
//     * 分组ID
//     */
//    @TableField("GROUP_ID")
//    private String groupId;


    /**
     * 马甲ID（外键关联DC_COURSE_PUPPET表）
     */
    @TableField("PUPPET_ID")
    private String puppetId;

    /**
     * 创建人
     */
    @TableField("CREATE_USER")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("MODIFY_USER")
    private String modifyUser;

    /**
     * 修改时间
     */
    @TableField("MODIFY_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    /**
     * 是否为组长（0-否，1-是）
     */
    @TableField("IS_GROUP_LEADER")
    private Integer isGroupLeader;

    /**
     * index
     */
    @TableField("STUDENT_INDEX")
    private Integer studentIndex;

    @TableField(exist = false)
    private String studentName;
    @TableField(exist = false)
    private String studentSex;
}