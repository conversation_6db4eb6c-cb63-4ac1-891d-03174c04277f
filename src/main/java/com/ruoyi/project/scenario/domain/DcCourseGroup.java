package com.ruoyi.project.scenario.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 课程分组实体类
 * 对应表：DC_COURSE_GROUP
 * 
 * <AUTHOR>
 */
@Data
@TableName("DC_COURSE_GROUP")
public class DcCourseGroup implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 分组ID
     */
    @TableId(value = "GROUP_ID", type = IdType.ASSIGN_ID)
    private String groupId;

    /**
     * 场景ID
     */
    @TableField("SCENE_ID")
    private String sceneId;

    /**
     * 分组名称
     */
    @TableField("GROUP_NAME")
    private String groupName;

    /**
     * 分组编号
     */
    @TableField("GROUP_NUM")
    private String groupNum;

    /**
     * 分组排序
     */
    @TableField("GROUP_ORDER")
    private Integer groupOrder;

    /**
     * 分组图标
     */
    @TableField("GROUP_ICON")
    private String groupIcon;

    /**
     * 创建人
     */
    @TableField("CREATE_USER")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("MODIFY_USER")
    private String modifyUser;

    /**
     * 修改时间
     */
    @TableField("MODIFY_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;
} 