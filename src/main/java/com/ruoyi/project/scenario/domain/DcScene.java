package com.ruoyi.project.scenario.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.framework.aspectj.lang.annotation.Excels;
import com.ruoyi.project.system.domain.SysDept;
import com.ruoyi.project.system.domain.SysUser;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 场景对象 dc_scene
 * 
 * <AUTHOR>
 * @date 2025-05-05
 */
@Data
@TableName("DC_SCENE")
public class DcScene implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "SCENE_ID", type = IdType.ASSIGN_ID)
    @Excel(name = "sceneId")
    private String sceneId;

    @TableField(value = "SCENE_NAME")
    @Excel(name = "sceneName")
    private String sceneName;

    @TableField(value = "SCENE_INTRODUCTION")
    @Excel(name = "sceneIntroduction")
    private String sceneIntroduction;

    @TableField(value = "SCENE_USER_IDNUMBER")
    @Excel(name = "sceneUserIdnumber")
    private Long sceneUserIdnumber;

    /**
     * 是否是公共场景
     */
    @Excel(name = "是否是公共场景")
    @TableField(value = "SCENE_ISPUBLIC")
    private String sceneIspublic;

    /**
     * 创建人
     */
    @Excel(name = "创建人")
    @TableField(value = "CREATE_USER")
    private String createUser;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "CREATE_TIME")
    @Excel(name = "createTime")
    private Date createTime;

    /**
     * 修改人
     */
    @Excel(name = "修改人")
    @TableField(value = "MODIFY_USER")
    private String modifyUser;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "MODIFY_TIME")
    private Date modifyTime;

    @Excel(name = "是否删除")
    @TableField(value = "ISDELETE")
    private String isdelete;

    @TableField("SCENE_TAGID")
    private String sceneTagid;

    @TableField("SCENE_IMAGE")
    private String sceneImage;

}
