package com.ruoyi.project.scenario.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

import java.io.Serializable;

/**
 * 场景内的马甲对象 dc_scene_puppet
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
@TableName("DC_SCENE_PUPPET")
public class DcScenePuppet implements Serializable
{
    private static final long serialVersionUID = 1L;

    @TableId(value = "PUPPET_ID", type = IdType.ASSIGN_ID)
    @Excel(name = "puppetId")
    private String puppetId;

    @TableField(value = "PUPPET_DESC")
    @Excel(name = "puppetDesc")
    private String puppetDesc;

    @TableField(value = "PUPPET_NAME")
    @Excel(name = "puppetName")
    private String puppetName;

    @TableField(value = "PUPPET_ICON")
    @Excel(name = "puppetIcon")
    private String puppetIcon;

    @TableField(value = "SCENE_ID")
    @Excel(name = "sceneId")
    private String sceneId;

    @TableField(value = "GROUP_ID")
    @Excel(name = "groupId")
    private String groupId;

    @TableField(value = "PUPPET_INDEX")
    @Excel(name = "排序号")
    private String puppetIndex;
}
