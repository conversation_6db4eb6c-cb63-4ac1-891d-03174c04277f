package com.ruoyi.project.scenario.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 课程阶段实体类
 * 对应表：DC_COURSE_STAGE
 * 
 * <AUTHOR>
 */
@Data
@TableName("DC_COURSE_STAGE")
public class DcCourseStage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 课程阶段ID
     */
    @TableId(value = "COURSE_STAGE_ID", type = IdType.ASSIGN_ID)
    private String courseStageId;

    /**
     * 课程ID
     */
    @TableField("COURSE_ID")
    private String courseId;

    /**
     * 课程阶段标题
     */
    @TableField("COURSE_STAGE_TITLE")
    private String courseStageTitle;

    /**
     * 课程阶段内容
     */
    @TableField("COURSE_STAGE_TEXT")
    private String courseStageText;

    /**
     * 课程阶段顺序
     */
    @TableField("COURSE_STAGE_ORDER")
    private Integer courseStageOrder;

    /**
     * 创建人
     */
    @TableField("CREATE_USER")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("MODIFY_USER")
    private String modifyUser;

    /**
     * 修改时间
     */
    @TableField("MODIFY_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;

    /**
     * 是否已完成（0:未完成, 1:已完成）
     */
    @TableField("IS_COMPLETED")
    private Integer isCompleted;

    /**
     * 阶段附件列表（关联查询，不存储到数据库）
     */
    @TableField(exist = false)
    private List<DcCourseStageAnnex> stageAnnexList;
} 