package com.ruoyi.project.teacher.service.impl;

import com.ruoyi.project.teacher.dto.TcCourseGroupDTO;
import com.ruoyi.project.teacher.mapper.TcCourseGroupMapper;
import com.ruoyi.project.teacher.service.ITcCourseGroupService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 教师端课程群组Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TcCourseGroupServiceImpl implements ITcCourseGroupService {

    private final TcCourseGroupMapper tcCourseGroupMapper;

    /**
     * 根据班级ID查询课程群组列表
     * 
     * @param deptId 班级ID
     * @return 课程群组列表
     */
    @Override
    public List<TcCourseGroupDTO> getCourseGroupsByDeptId(Long deptId) {
        log.debug("根据班级ID查询课程群组列表，deptId: {}", deptId);
        
        if (deptId == null) {
            log.warn("班级ID为空，返回包含公共聊天室的列表");
            // 即使班级ID为空，也返回公共聊天室
            return createPublicChatRoomList();
        }
        
        try {
            List<TcCourseGroupDTO> groups = tcCourseGroupMapper.selectCourseGroupsByDeptId(deptId);
            log.debug("查询到班级 {} 的群组数量: {}", deptId, groups.size());
            
            // 添加公共聊天室到列表开头
            List<TcCourseGroupDTO> resultList = createPublicChatRoomList();
            resultList.addAll(groups);
            
            log.debug("添加公共聊天室后，总群组数量: {}", resultList.size());
            return resultList;
        } catch (Exception e) {
            log.error("根据班级ID查询课程群组列表异常，deptId: {}", deptId, e);
            throw new RuntimeException("查询课程群组列表失败", e);
        }
    }

    /**
     * 根据教师ID查询其所在班级的课程群组列表
     * 
     * @param teacherId 教师ID
     * @return 课程群组列表
     */
    @Override
    public List<TcCourseGroupDTO> getCourseGroupsByTeacherId(Long teacherId) {
        log.debug("根据教师ID查询课程群组列表，teacherId: {}", teacherId);
        
        if (teacherId == null) {
            log.warn("教师ID为空，返回包含公共聊天室的列表");
            return createPublicChatRoomList();
        }
        
        try {
            List<TcCourseGroupDTO> groups = tcCourseGroupMapper.selectCourseGroupsByTeacherId(teacherId);
            log.debug("查询到教师 {} 的群组数量: {}", teacherId, groups.size());
            
            // 添加公共聊天室到列表开头
            List<TcCourseGroupDTO> resultList = createPublicChatRoomList();
            resultList.addAll(groups);
            
            log.debug("添加公共聊天室后，总群组数量: {}", resultList.size());
            return resultList;
        } catch (Exception e) {
            log.error("根据教师ID查询课程群组列表异常，teacherId: {}", teacherId, e);
            throw new RuntimeException("查询课程群组列表失败", e);
        }
    }

    /**
     * 根据班级代码查询课程和对应的群组列表
     * 
     * @param classCode 班级代码
     * @return 课程群组列表
     */
    @Override
    public List<TcCourseGroupDTO> getCourseGroupsByClassCode(String classCode) {
        log.debug("根据班级代码查询课程群组列表，classCode: {}", classCode);
        
        if (classCode == null || classCode.trim().isEmpty()) {
            log.warn("班级代码为空，返回包含公共聊天室的列表");
            return createPublicChatRoomList();
        }
        
        try {
            List<TcCourseGroupDTO> groups = tcCourseGroupMapper.selectCourseGroupsByClassCode(classCode);
            log.debug("查询到班级代码 {} 的群组数量: {}", classCode, groups.size());
            
            // 添加公共聊天室到列表开头
            List<TcCourseGroupDTO> resultList = createPublicChatRoomList();
            resultList.addAll(groups);
            
            log.debug("添加公共聊天室后，总群组数量: {}", resultList.size());
            return resultList;
        } catch (Exception e) {
            log.error("根据班级代码查询课程群组列表异常，classCode: {}", classCode, e);
            throw new RuntimeException("查询课程群组列表失败", e);
        }
    }

    /**
     * 根据课程代码查询课程和对应的群组列表
     *
     * @param courseCode 课程代码
     * @return 课程群组列表
     */
    @Override
    public List<TcCourseGroupDTO> getCourseGroupsByCourseCode(String courseCode) {
        log.debug("根据课程代码查询课程群组列表，courseCode: {}", courseCode);

        if (courseCode == null || courseCode.trim().isEmpty()) {
            log.warn("课程代码为空，返回包含公共聊天室的列表");
            return createPublicChatRoomList();
        }

        try {
            List<TcCourseGroupDTO> groups = tcCourseGroupMapper.selectCourseGroupsByCourseCode(courseCode);
            log.debug("查询到课程代码 {} 的群组数量: {}", courseCode, groups.size());

            // 添加公共聊天室到列表开头
            List<TcCourseGroupDTO> resultList = createPublicChatRoomList();
            resultList.addAll(groups);

            log.debug("添加公共聊天室后，总群组数量: {}", resultList.size());
            return resultList;
        } catch (Exception e) {
            log.error("根据课程代码查询课程群组列表异常，courseCode: {}", courseCode, e);
            throw new RuntimeException("查询课程群组列表失败", e);
        }
    }

    /**
     * 创建公共聊天室列表
     *
     * @return 包含公共聊天室的列表
     */
    private List<TcCourseGroupDTO> createPublicChatRoomList() {
        List<TcCourseGroupDTO> resultList = new ArrayList<>();

        // 创建公共聊天室对象
        TcCourseGroupDTO publicChatRoom = new TcCourseGroupDTO();
        publicChatRoom.setGroupId("public");
        publicChatRoom.setGroupName("公共聊天室");
        publicChatRoom.setGroupNum("PUBLIC");
        publicChatRoom.setGroupOrder(0);
        publicChatRoom.setCourseId("");
        publicChatRoom.setCourseName("公共聊天");
        publicChatRoom.setSceneId("");
        publicChatRoom.setSceneName("公共聊天室");
        publicChatRoom.setStudentCount(0); // 教师端监控，不显示具体人数

        resultList.add(publicChatRoom);
        return resultList;
    }
}