package com.ruoyi.project.teacher.controller;

import com.ruoyi.framework.security.LoginUser;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.teacher.dto.TcCourseGroupDTO;
import com.ruoyi.project.teacher.service.ITcCourseGroupService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 教师端-群组管理Controller
 * 
 * 注意：群组列表中包含课程大群（以课程ID作为群组ID，课程名称作为群组名称）
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@RequiredArgsConstructor
@Slf4j
@RestController
@RequestMapping("/teacher/chat/group")
public class TcChatGroupController extends BaseController {

    private final ITcCourseGroupService tcCourseGroupService;

    /**
     * 查询当前教师所在班级的所有群组列表
     * 
     * @return 群组列表
     */
    @GetMapping("/my-dept-groups")
    public AjaxResult getMyDeptGroups() {
        Long deptId = getDeptId();
        if (deptId == null) {
            return AjaxResult.error("获取当前用户班级信息失败");
        }

        List<TcCourseGroupDTO> groups = tcCourseGroupService.getCourseGroupsByDeptId(deptId);
        return AjaxResult.success(groups);
    }

    /**
     * 根据deptId查询班级的所有群组列表
     * 
     * @param deptId 班级ID
     * @return 群组列表
     */
    @GetMapping("/dept/{deptId}")
    public AjaxResult getGroupsByDeptId(@PathVariable("deptId") Long deptId) {
        if (deptId == null) {
            return AjaxResult.error("班级ID不能为空");
        }

        List<TcCourseGroupDTO> groups = tcCourseGroupService.getCourseGroupsByDeptId(deptId);
        return AjaxResult.success(groups);
    }

    /**
     * 根据班级代码查询课程和对应的群组列表
     * 
     * @param classCode 班级代码
     * @return 课程群组列表
     */
    @GetMapping("/class/{classCode}")
    public AjaxResult getGroupsByClassCode(@PathVariable("classCode") String classCode) {
        if (classCode == null || classCode.trim().isEmpty()) {
            return AjaxResult.error("班级代码不能为空");
        }

        List<TcCourseGroupDTO> groups = tcCourseGroupService.getCourseGroupsByClassCode(classCode);
        return AjaxResult.success(groups);
    }

    /**
     * 根据课程代码查询课程和对应的群组列表
     * 
     * @param courseCode 课程代码
     * @return 课程群组列表
     */
    @GetMapping("/course/{courseCode}")
    public AjaxResult getGroupsByCourseCode(@PathVariable("courseCode") String courseCode) {
        if (courseCode == null || courseCode.trim().isEmpty()) {
            return AjaxResult.error("课程代码不能为空");
        }

        List<TcCourseGroupDTO> groups = tcCourseGroupService.getCourseGroupsByCourseCode(courseCode);
        return AjaxResult.success(groups);
    }
}
