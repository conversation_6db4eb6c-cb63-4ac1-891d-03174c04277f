package com.ruoyi.project.teacher.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 教师端课程群组DTO
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@NoArgsConstructor 
@AllArgsConstructor
public class TcCourseGroupDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分组ID
     */
    private String groupId;

    /**
     * 场景ID
     */
    private String sceneId;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 分组编号
     */
    private String groupNum;

    /**
     * 分组排序
     */
    private Integer groupOrder;

    /**
     * 分组图标
     */
    private String groupIcon;

    /**
     * 课程ID
     */
    private String courseId;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 班级ID
     */
    private Long deptId;

    /**
     * 班级名称
     */
    private String deptName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 学生数量
     */
    private Integer studentCount;
} 