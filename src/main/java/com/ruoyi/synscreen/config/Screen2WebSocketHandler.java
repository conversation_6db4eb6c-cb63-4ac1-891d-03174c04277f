package com.ruoyi.synscreen.config;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.HashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.TimeUnit;

@Component
@ServerEndpoint(value = "/ws/screen/{userId}")
@Slf4j
public class Screen2WebSocketHandler {

    // 定义一个静态的HashMap，用于存储用户ID和对应的会话集合
    private static final HashMap<String,CopyOnWriteArraySet<Session>> sessionMap=new HashMap<>();
    @OnOpen
    public void onOpen(@PathParam("userId") String userId, Session session, EndpointConfig config) {

        // 如果sessionMap中不存在该用户ID，则创建一个新的CopyOnWriteArraySet，并将session添加进去
        sessionMap.computeIfAbsent(userId, k -> new CopyOnWriteArraySet<>()).add(session);
        log.info("打开连接触发事件!用户 {} 已连接", userId);
        log.info("用户 {} 当前连接数: {}", userId, sessionMap.get(userId).size());

    }
    @OnClose
    public void onclose(Session session){
        // 遍历所有用户ID，移除对应的会话
        for (CopyOnWriteArraySet<Session> sessions : sessionMap.values()) {
            sessions.remove(session);
        }
        // 清理空的用户会话集合
        sessionMap.entrySet().removeIf(entry -> entry.getValue().isEmpty());
    }

    @OnMessage
    public void onmessage(Session session,String message)
    {
        log.info("收到消息: {}", message);

        // 从session中获取userId
        String userId = null;
        try {
            userId = (String) session.getUserProperties().get("userId");
            if (userId == null) {
                // 如果session属性中没有userId，尝试从路径参数获取
                // 注意：这里需要根据实际情况获取路径参数
                // 下面的代码是示例，可能需要根据你的环境调整
                userId = session.getPathParameters().get("userId");
            }
        } catch (Exception e) {
            log.error("获取用户ID失败", e);
            return;
        }

        if (userId != null) {
            sendMessageToUser(userId, message);
        } else {
            log.warn("无法确定消息发送的目标用户ID");
        }
    }

    // 发送消息给指定用户ID的所有会话
    public void sendMessageToUser(String userId, String message) {
        CopyOnWriteArraySet<Session> sessions = sessionMap.get(userId);
        if (sessions == null || sessions.isEmpty()) {
            log.info("用户 {} 没有活跃的会话", userId);
            return;
        }

        log.info("向用户 {} 的 {} 个会话发送消息", userId, sessions.size());

        // 遍历用户的所有会话并发送消息
        for (Session session : sessions) {
            sendMessageWithRetry(session, message);
        }
    }

    // 带重试机制的消息发送方法
    private void sendMessageWithRetry(Session session, String message) {
        // 设置重试次数
        int retries = 3;
        // 循环重试
        for (int i = 0; i < retries; i++) {
            try {
                // 判断会话是否打开
                if (session != null && session.isOpen()) {
                    // 使用异步发送，避免阻塞
                    session.getAsyncRemote().sendText(message);
                    log.debug("消息发送成功");
                    // 发送成功后跳出循环
                    break;
                } else {
                    log.info("会话已关闭，无法发送消息");
                    // 会话关闭后跳出循环
                    break;
                }
            } catch (Exception e) {
                // 达到最大重试次数后记录错误日志
                if (i == retries - 1) {
                    log.error("消息发送失败，已达到最大重试次数", e);
                } else {
                    // 记录警告日志，并尝试重试
                    log.warn("消息发送失败，尝试重试 ({}/{}): {}", i + 1, retries, e.getMessage());
                    try {
                        TimeUnit.MILLISECONDS.sleep(100); // 短暂等待后重试
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }
    }




}