<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.student.mapper.StSurveyMapper">

    <resultMap type="com.ruoyi.project.student.domain.StDcCourseTestpaper" id="SurveyDetailResult">
        <result property="paperId" column="PAPER_ID" />
        <result property="paperTitle" column="PAPER_TITLE" />
        <result property="sceneId" column="SCENE_ID" />
        <result property="createUser" column="CREATE_USER" />
        <result property="createTime" column="CREATE_TIME" />
        <result property="modifyUser" column="MODIFY_USER" />
        <result property="modifyTime" column="MODIFY_TIME" />
        <collection property="questions" ofType="com.ruoyi.project.student.domain.StDcCourseQuestion">
            <result property="questionId" column="QUESTION_ID" />
            <result property="questonText" column="QUESTON_TEXT" />
            <result property="questionType" column="QUESTION_TYPE" />
            <result property="paperId" column="QUESTION_PAPER_ID" />
            <result property="questionAnswer" column="QUESTION_ANSWER" />
            <result property="questionPoint" column="QUESTION_POINT" />
            <collection property="questionItems" ofType="com.ruoyi.project.student.domain.StDcCourseQuestionItem">
                <result property="itemId" column="ITEM_ID" />
                <result property="questionId" column="ITEM_QUESTION_ID" />
                <result property="itemText" column="ITEM_TEXT" />
                <result property="isRight" column="IS_RIGHT" />
            </collection>
        </collection>
    </resultMap>

    <resultMap type="com.ruoyi.project.student.domain.StDcCourseQuestion" id="QuestionResult">
        <result property="questionId" column="QUESTION_ID" />
        <result property="questonText" column="QUESTON_TEXT" />
        <result property="questionType" column="QUESTION_TYPE" />
        <result property="paperId" column="PAPER_ID" />
        <result property="questionAnswer" column="QUESTION_ANSWER" />
        <result property="questionPoint" column="QUESTION_POINT" />
        <collection property="questionItems" ofType="com.ruoyi.project.student.domain.StDcCourseQuestionItem">
            <result property="itemId" column="ITEM_ID" />
            <result property="questionId" column="ITEM_QUESTION_ID" />
            <result property="itemText" column="ITEM_TEXT" />
            <result property="isRight" column="IS_RIGHT" />
        </collection>
    </resultMap>

    <resultMap type="com.ruoyi.project.student.domain.StDcCourseQuestionItem" id="QuestionItemResult">
        <result property="itemId" column="ITEM_ID" />
        <result property="questionId" column="QUESTION_ID" />
        <result property="itemText" column="ITEM_TEXT" />
        <result property="isRight" column="IS_RIGHT" />
        <result property="createUser" column="CREATE_USER" />
        <result property="createTime" column="CREATE_TIME" />
        <result property="modifyUser" column="MODIFY_USER" />
        <result property="modifyTime" column="MODIFY_TIME" />
    </resultMap>

    <!-- 根据问卷ID查询问卷详情（包含题目和选项） -->
    <select id="selectSurveyDetailById" parameterType="string" resultMap="SurveyDetailResult">
        SELECT
            ctp.PAPER_ID,
            ctp.PAPER_TITLE,
            c.SCENE_ID,
            ctp.CREATE_USER,
            ctp.CREATE_TIME,
            ctp.MODIFY_USER,
            ctp.MODIFY_TIME,
            cq.QUESTION_ID,
            cq.QUESTON_TEXT,
            cq.QUESTION_TYPE,
            cq.PAPER_ID as QUESTION_PAPER_ID,
            cq.QUESTION_ANSWER,
            cq.QUESTION_POINT,
            cqi.ITEM_ID,
            cqi.QUESTION_ID as ITEM_QUESTION_ID,
            cqi.ITEM_TEXT,
            cqi.IS_RIGHT
        FROM DC_COURSE_TESTPAPER ctp
        LEFT JOIN DC_COURSE_QUESTION cq ON ctp.PAPER_ID = cq.PAPER_ID
        LEFT JOIN DC_COURSE_QUESTION_ITEM cqi ON cq.QUESTION_ID = cqi.QUESTION_ID
        LEFT JOIN DC_COURSE_PAPER_HISTORY_ALL cpha ON ctp.PAPER_ID = cpha.PAPER_ID
        LEFT JOIN DC_COURSE c ON cpha.COURSE_ID = c.COURSE_ID
        WHERE ctp.PAPER_ID = #{paperId}
        ORDER BY cq.QUESTION_INDEX ASC, cq.CREATE_TIME ASC, cqi.ITEM_INDEX ASC, cqi.CREATE_TIME ASC
    </select>

    <!-- 根据问卷ID查询题目列表 -->
    <select id="selectQuestionsByPaperId" parameterType="string" resultMap="QuestionResult">
        SELECT
            cq.QUESTION_ID,
            cq.QUESTON_TEXT,
            cq.QUESTION_TYPE,
            cq.PAPER_ID,
            cq.QUESTION_ANSWER,
            cq.QUESTION_POINT,
            cqi.ITEM_ID,
            cqi.QUESTION_ID as ITEM_QUESTION_ID,
            cqi.ITEM_TEXT,
            cqi.IS_RIGHT
        FROM DC_COURSE_QUESTION cq
        LEFT JOIN DC_COURSE_QUESTION_ITEM cqi ON cq.QUESTION_ID = cqi.QUESTION_ID
        WHERE cq.PAPER_ID = #{paperId}
        ORDER BY cq.QUESTION_INDEX ASC, cq.CREATE_TIME ASC, cqi.ITEM_INDEX ASC, cqi.CREATE_TIME ASC
    </select>

    <!-- 根据题目ID查询选项列表 -->
    <select id="selectItemsByQuestionId" parameterType="string" resultMap="QuestionItemResult">
        SELECT
            ITEM_ID,
            QUESTION_ID,
            ITEM_TEXT,
            IS_RIGHT,
            CREATE_USER,
            CREATE_TIME,
            MODIFY_USER,
            MODIFY_TIME
        FROM DC_COURSE_QUESTION_ITEM
        WHERE QUESTION_ID = #{questionId}
        ORDER BY ITEM_INDEX ASC, CREATE_TIME ASC
    </select>

    <!-- 检查学生是否已提交该问卷 -->
    <select id="hasStudentSubmitted" resultType="boolean">
        SELECT CASE WHEN COUNT(1) > 0 THEN 1 ELSE 0 END FROM DC_COURSE_PAPER_HISTORY
        WHERE PAPER_ID = #{paperId} 
        AND STUDENT_CODE = #{studentCode} 
        AND PAPER_SUBMIT = 'Y'
    </select>

    <!-- 根据场景ID查询问卷列表 -->
    <select id="selectSurveyListBySceneId" parameterType="string" resultType="map">
        SELECT
            ctp.PAPER_ID as paperId,
            ctp.PAPER_TITLE as paperTitle
        FROM DC_COURSE_TESTPAPER ctp
        LEFT JOIN DC_COURSE_PAPER_HISTORY_ALL cpha ON ctp.PAPER_ID = cpha.PAPER_ID
        LEFT JOIN DC_COURSE c ON cpha.COURSE_ID = c.COURSE_ID
        WHERE c.SCENE_ID = #{sceneId}
            AND cpha.IS_DEL != '1'
        ORDER BY cpha.CREATE_TIME ASC
    </select>

    <!-- 根据场景ID查询问卷列表（包含提交状态） -->
    <select id="selectSurveyListWithSubmissionStatus" resultType="map">
        SELECT
            ctp.PAPER_ID as paperId,
            ctp.PAPER_TITLE as paperTitle,
            CASE WHEN cph.PAPER_SUBMIT = 'Y' THEN 1 ELSE 0 END as hasSubmitted
        FROM DC_COURSE_TESTPAPER ctp
        LEFT JOIN DC_COURSE_PAPER_HISTORY_ALL cpha ON ctp.PAPER_ID = cpha.PAPER_ID
        LEFT JOIN DC_COURSE c ON cpha.COURSE_ID = c.COURSE_ID
        LEFT JOIN DC_COURSE_PAPER_HISTORY cph
            ON ctp.PAPER_ID = cph.PAPER_ID
            AND cph.STUDENT_CODE = #{studentCode}
            AND cph.PAPER_SUBMIT = 'Y'
        WHERE c.SCENE_ID = #{sceneId}
            AND cpha.IS_DEL != '1'
        ORDER BY cpha.CREATE_TIME ASC
    </select>

</mapper> 