<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.student.mapper.StSysUserMapper">
    
    <select id="selectStudentCourseInfo" resultType="com.ruoyi.project.student.domain.dto.StudentCourseInfoDTO">
        SELECT 
            u.USER_NAME AS userName,
            u.NICK_NAME AS nickName,
            u.AVATAR AS avatar,
            c_group.GROUP_ID AS groupId,
            c_group.GROUP_NAME AS groupName,
            c_student.PUPPET_NAME AS puppetName,
            c_student.PUPPET_ICON AS puppetIcon,
            course.COURSE_ID AS courseId,
            course.COURSE_NAME AS courseName,
            course.COURSE_INTRODUCTION AS courseIntroduction,
            scene.scene_id AS sceneId,
            scene.SCENE_NAME AS sceneName,
            scene.SCENE_INTRODUCTION AS sceneIntroduction,
            CASE 
                WHEN c_student.IS_GROUP_LEADER = 1 THEN 1 
                ELSE 0 
            END AS isGroupLeader
        FROM 
            SYS_USER u
        LEFT JOIN DC_COURSE_STUDENT c_student
            ON u.USER_NAME = c_student.STUDENT_CODE
        LEFT JOIN DC_COURSE_GROUP c_group
            ON c_student.GROUP_ID = c_group.GROUP_ID
        LEFT JOIN DC_COURSE course
            ON c_group.SCENE_ID = course.SCENE_ID
        LEFT JOIN DC_SCENE scene
            ON course.SCENE_ID = scene.SCENE_ID
        WHERE 
            <if test="userName != null and userName != ''">
                u.USER_NAME = #{userName}
            </if>
            <if test="userId != null">
                <if test="userName != null and userName != ''">OR</if>
                u.USER_ID = #{userId}
            </if>
    </select>
    
    <select id="selectGroupUsersByGroupId" resultType="com.ruoyi.project.student.dto.GroupUserDTO">
        SELECT
            su.user_id AS userId,
            su.user_name AS userName,
            su.nick_name AS nickName,
            su.avatar AS avatar,
            dcs.PUPPET_NAME AS puppetName,
            dcs.PUPPET_ICON AS puppetIcon,
            dcs.IS_GROUP_LEADER as isGroupLeader
        FROM
            DC_COURSE_STUDENT dcs
        LEFT JOIN
            SYS_USER su ON dcs.STUDENT_CODE = su.USER_NAME
        WHERE
            dcs.GROUP_ID = #{groupId}
            AND su.status = '0'
            AND su.del_flag = '0'
    </select>
    
</mapper> 