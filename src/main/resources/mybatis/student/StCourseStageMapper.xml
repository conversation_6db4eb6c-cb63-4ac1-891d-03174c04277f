<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.student.mapper.StCourseStageMapper">

    <resultMap type="com.ruoyi.project.student.domain.StDcCourseStage" id="StCourseStageResult">
        <result property="courseStageId"    column="course_stage_id"    />
        <result property="courseId"         column="course_id"          />
        <result property="courseStageTitle" column="course_stage_title" />
        <result property="courseStageText"  column="course_stage_text"  />
        <result property="courseStageOrder" column="course_stage_order" />
        <result property="createUser"       column="create_user"        />
        <result property="createTime"       column="create_time"        />
        <result property="modifyUser"       column="modify_user"        />
        <result property="modifyTime"       column="modify_time"        />
        <result property="isCompleted"      column="is_completed"       />
    </resultMap>

    <resultMap type="com.ruoyi.project.student.domain.StDcCourseStage" id="StCourseStageWithAnnexResult" extends="StCourseStageResult">
        <collection property="stageAnnexList" ofType="com.ruoyi.project.student.domain.StDcCourseStageAnnex">
            <result property="annexId"    column="annex_id"    />
            <result property="stageId"    column="stage_id"    />
            <result property="annexName"  column="annex_name"  />
            <result property="annexPath"  column="annex_path"  />
            <result property="createUser" column="annex_create_user" />
            <result property="createTime" column="annex_create_time" />
            <result property="modifyUser" column="annex_modify_user" />
            <result property="modifyTime" column="annex_modify_time" />
        </collection>
    </resultMap>

    <select id="selectCourseStageListWithAnnex" parameterType="String" resultMap="StCourseStageWithAnnexResult">
        SELECT 
            cs.COURSE_STAGE_ID as course_stage_id,
            cs.COURSE_ID as course_id,
            cs.COURSE_STAGE_TITLE as course_stage_title,
            cs.COURSE_STAGE_TEXT as course_stage_text,
            cs.COURSE_STAGE_ORDER as course_stage_order,
            cs.CREATE_USER as create_user,
            cs.CREATE_TIME as create_time,
            cs.MODIFY_USER as modify_user,
            cs.MODIFY_TIME as modify_time,
            cs.IS_COMPLETED as is_completed,
            csa.ANNEX_ID as annex_id,
            csa.STAGE_ID as stage_id,
            csa.ANNEX_NAME as annex_name,
            csa.ANNEX_PATH as annex_path,
            csa.CREATE_USER as annex_create_user,
            csa.CREATE_TIME as annex_create_time,
            csa.MODIFY_USER as annex_modify_user,
            csa.MODIFY_TIME as annex_modify_time
        FROM DC_COURSE_STAGE cs
        LEFT JOIN DC_COURSE_STAGE_ANNEX csa ON cs.COURSE_STAGE_ID = csa.STAGE_ID
        WHERE cs.COURSE_ID = #{courseId}
        ORDER BY cs.COURSE_STAGE_ORDER ASC, csa.CREATE_TIME ASC
    </select>

</mapper> 