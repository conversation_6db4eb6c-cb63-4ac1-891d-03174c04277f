<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.teacher.mapper.TcSurveyMapper">

    <resultMap type="com.ruoyi.project.teacher.dto.TcSurveyListDTO" id="TcSurveyListResult">
        <result property="paperId" column="paper_id"/>
        <result property="paperTitle" column="paper_title"/>
        <result property="sceneId" column="scene_id"/>
        <result property="sceneName" column="scene_name"/>
        <result property="courseId" column="course_id"/>
        <result property="courseName" column="course_name"/>
        <result property="courseCode" column="course_code"/>
        <result property="createTime" column="create_time"/>
        <result property="totalStudents" column="total_students"/>
        <result property="submittedCount" column="submitted_count"/>
        <result property="questionCount" column="question_count"/>
    </resultMap>

    <resultMap type="com.ruoyi.project.teacher.dto.TcSurveyQuestionDTO" id="TcSurveyQuestionResult">
        <result property="questionId" column="questionId"/>
        <result property="questionText" column="questionText"/>
        <result property="questionType" column="questionType"/>
    </resultMap>

    <resultMap type="com.ruoyi.project.teacher.dto.TcSurveyOptionDTO" id="TcSurveyOptionResult">
        <result property="questionId" column="questionId"/>
        <result property="itemId" column="itemId"/>
        <result property="itemText" column="itemText"/>
        <result property="isRight" column="isRight"/>
    </resultMap>

    <resultMap type="com.ruoyi.project.teacher.dto.TcSurveyBasicInfoDTO" id="TcSurveyBasicInfoResult">
        <result property="paperId" column="paperId"/>
        <result property="paperTitle" column="paperTitle"/>
        <result property="sceneName" column="sceneName"/>
        <result property="createTime" column="createTime"/>
    </resultMap>

    <!-- 根据课程代码查询问卷列表（含提交统计） -->
    <select id="selectSurveyListByCourseCode" parameterType="String" resultMap="TcSurveyListResult">
        SELECT
            cpha.PAPER_ID as paper_id,
            ctp.PAPER_TITLE as paper_title,
            c.SCENE_ID as scene_id,
            s.SCENE_NAME as scene_name,
            c.COURSE_ID as course_id,
            c.COURSE_NAME as course_name,
            c.COURSE_CODE as course_code,
            cpha.CREATE_TIME as create_time,
            COALESCE(student_count.total_students, 0) as total_students,
            COALESCE(submitted_count.submitted_count, 0) as submitted_count,
            COALESCE(question_count.question_count, 0) as question_count
        FROM DC_COURSE_PAPER_HISTORY_ALL cpha
        LEFT JOIN DC_COURSE_TESTPAPER ctp ON cpha.PAPER_ID = ctp.PAPER_ID
        LEFT JOIN DC_COURSE c ON cpha.COURSE_ID = c.COURSE_ID
        LEFT JOIN DC_SCENE s ON c.SCENE_ID = s.SCENE_ID
        LEFT JOIN (
            SELECT 
                c2.COURSE_ID,
                COUNT(DISTINCT cs.STUDENT_CODE) as total_students
            FROM DC_COURSE c2
            LEFT JOIN DC_COURSE_GROUP cg ON c2.COURSE_ID = cg.COURSE_ID
            LEFT JOIN DC_COURSE_PUPPET cpt ON cg.GROUP_ID = cpt.GROUP_ID
            LEFT JOIN DC_COURSE_STUDENT cs ON cpt.PUPPET_ID = cs.PUPPET_ID
            INNER JOIN SYS_USER u_student ON cs.STUDENT_CODE = u_student.USER_NAME
            WHERE c2.COURSE_CODE = #{courseCode}
                AND u_student.STATUS = '0' AND u_student.DEL_FLAG = '0'
            GROUP BY c2.COURSE_ID
        ) student_count ON c.COURSE_ID = student_count.COURSE_ID
        LEFT JOIN (
            SELECT
                cph.PAPER_ID,
                COUNT(DISTINCT cph.STUDENT_CODE) as submitted_count
            FROM DC_COURSE_PAPER_HISTORY cph
            WHERE cph.PAPER_SUBMIT = 'Y'
            GROUP BY cph.PAPER_ID
        ) submitted_count ON cpha.PAPER_ID = submitted_count.PAPER_ID
        LEFT JOIN (
            SELECT
                ctp2.PAPER_ID,
                COUNT(*) as question_count
            FROM DC_COURSE_TESTPAPER ctp2
            LEFT JOIN DC_COURSE_QUESTION cq ON ctp2.PAPER_ID = cq.PAPER_ID
            GROUP BY ctp2.PAPER_ID
        ) question_count ON cpha.PAPER_ID = question_count.PAPER_ID
        WHERE c.COURSE_CODE = #{courseCode}
            AND c.ISDELETE != '1'
            AND cpha.IS_DEL != '1'
        ORDER BY cpha.CREATE_TIME ASC
    </select>

    <!-- 根据问卷ID查询问卷基本信息 -->
    <select id="selectSurveyBasicInfo" parameterType="String" resultMap="TcSurveyBasicInfoResult">
        SELECT
            cpha.PAPER_ID as paperId,
            ctp.PAPER_TITLE as paperTitle,
            s.SCENE_NAME as sceneName,
            cpha.CREATE_TIME as createTime
        FROM DC_COURSE_PAPER_HISTORY_ALL cpha
        LEFT JOIN DC_COURSE_TESTPAPER ctp ON cpha.PAPER_ID = ctp.PAPER_ID
        LEFT JOIN DC_COURSE c ON cpha.COURSE_ID = c.COURSE_ID
        LEFT JOIN DC_SCENE s ON c.SCENE_ID = s.SCENE_ID
        WHERE cpha.PAPER_ID = #{paperId}
            AND cpha.IS_DEL != '1'
    </select>

    <!-- 根据问卷ID查询问卷题目列表 -->
    <select id="selectQuestionsByPaperId" parameterType="String" resultMap="TcSurveyQuestionResult">
        SELECT
            cq.QUESTION_ID as questionId,
            cq.QUESTON_TEXT as questionText,
            cq.QUESTION_TYPE as questionType
        FROM DC_COURSE_TESTPAPER ctp
        LEFT JOIN DC_COURSE_QUESTION cq ON ctp.PAPER_ID = cq.PAPER_ID
        WHERE ctp.PAPER_ID = #{paperId}
        ORDER BY cq.QUESTION_INDEX ASC, cq.CREATE_TIME ASC
    </select>

    <!-- 根据题目ID查询选项列表 -->
    <select id="selectOptionsByQuestionId" parameterType="String" resultMap="TcSurveyOptionResult">
        SELECT
            QUESTION_ID as questionId,
            ITEM_ID as itemId,
            ITEM_TEXT as itemText,
            IS_RIGHT as isRight
        FROM DC_COURSE_QUESTION_ITEM
        WHERE QUESTION_ID = #{questionId}
        ORDER BY ITEM_INDEX ASC, CREATE_TIME ASC
    </select>

    <!-- 根据问卷ID批量查询所有题目的选项列表 -->
    <select id="selectAllOptionsByPaperId" parameterType="String" resultMap="TcSurveyOptionResult">
        SELECT
            cqi.QUESTION_ID as questionId,
            cqi.ITEM_ID as itemId,
            cqi.ITEM_TEXT as itemText,
            cqi.IS_RIGHT as isRight
        FROM DC_COURSE_TESTPAPER ctp
        LEFT JOIN DC_COURSE_QUESTION cq ON ctp.PAPER_ID = cq.PAPER_ID
        INNER JOIN DC_COURSE_QUESTION_ITEM cqi ON cq.QUESTION_ID = cqi.QUESTION_ID
        WHERE ctp.PAPER_ID = #{paperId}
        ORDER BY cq.QUESTION_INDEX ASC, cq.CREATE_TIME ASC, cqi.ITEM_INDEX ASC, cqi.CREATE_TIME ASC
    </select>

    <!-- 根据问卷ID查询总学生数 -->
    <select id="selectTotalStudentsByPaperId" parameterType="String" resultType="Integer">
        SELECT COUNT(DISTINCT cph.STUDENT_CODE)
        FROM DC_COURSE_PAPER_HISTORY cph
        INNER JOIN SYS_USER u_student ON cph.STUDENT_CODE = u_student.USER_NAME
        WHERE cph.PAPER_ID = #{paperId}
            AND u_student.STATUS = '0' AND u_student.DEL_FLAG = '0'
    </select>

    <!-- 根据问卷ID查询已提交数量 -->
    <select id="selectSubmittedCountByPaperId" parameterType="String" resultType="Integer">
        SELECT COUNT(DISTINCT cph.STUDENT_CODE)
        FROM DC_COURSE_PAPER_HISTORY cph
        WHERE cph.PAPER_ID = #{paperId} 
        AND cph.PAPER_SUBMIT = 'Y'
    </select>

    <!-- 根据题目ID和选项ID查询选择次数 -->
    <select id="selectOptionSelectCount" resultType="Integer">
        SELECT COUNT(*)
        FROM DC_COURSE_QUESTION_HISTORY cqh
        WHERE cqh.QUESTION_ID = #{questionId}
        AND (
            cqh.MY_ANSWER = #{itemId}
            OR cqh.MY_ANSWER LIKE CONCAT('%', #{itemId}, '%')
        )
    </select>

    <!-- 根据题目ID查询回答总数 -->
    <select id="selectQuestionAnswerCount" parameterType="String" resultType="Integer">
        SELECT COUNT(*)
        FROM DC_COURSE_QUESTION_HISTORY cqh
        WHERE cqh.QUESTION_ID = #{questionId}
        AND cqh.MY_ANSWER IS NOT NULL
        AND cqh.MY_ANSWER != ''
    </select>

</mapper> 