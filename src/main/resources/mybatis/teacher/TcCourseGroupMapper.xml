<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.teacher.mapper.TcCourseGroupMapper">

    <resultMap type="com.ruoyi.project.teacher.dto.TcCourseGroupDTO" id="TcCourseGroupResult">
        <result property="groupId"       column="group_id"      />
        <result property="sceneId"       column="scene_id"      />
        <result property="groupName"     column="group_name"    />
        <result property="groupNum"      column="group_num"     />
        <result property="groupOrder"    column="group_order"   />
        <result property="groupIcon"     column="group_icon"    />
        <result property="courseId"      column="course_id"     />
        <result property="courseName"    column="course_name"   />
        <result property="sceneName"     column="scene_name"    />
        <result property="deptId"        column="dept_id"       />
        <result property="deptName"      column="dept_name"     />
        <result property="createTime"    column="create_time"   />
        <result property="studentCount"  column="student_count" />
    </resultMap>

    <select id="selectCourseGroupsByDeptId" parameterType="Long" resultMap="TcCourseGroupResult">
        SELECT 
            cg.GROUP_ID as group_id,
            cg.SCENE_ID as scene_id,
            cg.GROUP_NAME as group_name,
            cg.GROUP_NUM as group_num,
            cg.GROUP_ORDER as group_order,
            cg.GROUP_ICON as group_icon,
            c.COURSE_ID as course_id,
            c.COURSE_NAME as course_name,
            s.SCENE_NAME as scene_name,
            d.DEPT_ID as dept_id,
            d.DEPT_NAME as dept_name,
            cg.CREATE_TIME as create_time,
            COALESCE(cs_count.student_count, 0) as student_count
        FROM DC_COURSE_GROUP cg
        LEFT JOIN DC_COURSE c ON cg.SCENE_ID = c.SCENE_ID
        LEFT JOIN DC_SCENE s ON c.SCENE_ID = s.SCENE_ID
        LEFT JOIN SYS_USER u ON c.COURSE_USER_IDNUMBER = u.USER_ID
        LEFT JOIN SYS_DEPT d ON u.DEPT_ID = d.DEPT_ID
        LEFT JOIN (
            SELECT 
                cs.GROUP_ID,
                COUNT(*) as student_count
            FROM DC_COURSE_STUDENT cs
            GROUP BY cs.GROUP_ID
        ) cs_count ON cg.GROUP_ID = cs_count.GROUP_ID
        WHERE d.DEPT_ID = #{deptId}
        ORDER BY cg.GROUP_ORDER ASC, cg.CREATE_TIME ASC
    </select>

    <select id="selectCourseGroupsByTeacherId" parameterType="Long" resultMap="TcCourseGroupResult">
        SELECT 
            cg.GROUP_ID as group_id,
            cg.SCENE_ID as scene_id,
            cg.GROUP_NAME as group_name,
            cg.GROUP_NUM as group_num,
            cg.GROUP_ORDER as group_order,
            cg.GROUP_ICON as group_icon,
            c.COURSE_ID as course_id,
            c.COURSE_NAME as course_name,
            s.SCENE_NAME as scene_name,
            d.DEPT_ID as dept_id,
            d.DEPT_NAME as dept_name,
            cg.CREATE_TIME as create_time,
            COALESCE(cs_count.student_count, 0) as student_count
        FROM DC_COURSE_GROUP cg
        LEFT JOIN DC_COURSE c ON cg.SCENE_ID = c.SCENE_ID
        LEFT JOIN DC_SCENE s ON c.SCENE_ID = s.SCENE_ID
        LEFT JOIN SYS_USER u ON c.COURSE_USER_IDNUMBER = u.USER_ID
        LEFT JOIN SYS_DEPT d ON u.DEPT_ID = d.DEPT_ID
        LEFT JOIN (
            SELECT 
                cs.GROUP_ID,
                COUNT(*) as student_count
            FROM DC_COURSE_STUDENT cs
            GROUP BY cs.GROUP_ID
        ) cs_count ON cg.GROUP_ID = cs_count.GROUP_ID
        WHERE u.USER_ID = #{teacherId}
        ORDER BY cg.GROUP_ORDER ASC, cg.CREATE_TIME ASC
    </select>

    <select id="selectCourseGroupsByClassCode" parameterType="String" resultMap="TcCourseGroupResult">
        SELECT 
            cg.GROUP_ID as group_id,
            cg.SCENE_ID as scene_id,
            cg.GROUP_NAME as group_name,
            cg.GROUP_NUM as group_num,
            cg.GROUP_ORDER as group_order,
            cg.GROUP_ICON as group_icon,
            c.COURSE_ID as course_id,
            c.COURSE_NAME as course_name,
            s.SCENE_NAME as scene_name,
            d.DEPT_ID as dept_id,
            d.DEPT_NAME as dept_name,
            cg.CREATE_TIME as create_time,
            COALESCE(cs_count.student_count, 0) as student_count
        FROM DC_COURSE_GROUP cg
        LEFT JOIN DC_COURSE c ON cg.SCENE_ID = c.SCENE_ID
        LEFT JOIN DC_SCENE s ON c.SCENE_ID = s.SCENE_ID
        LEFT JOIN SYS_USER u ON c.COURSE_USER_IDNUMBER = u.USER_ID
        LEFT JOIN SYS_DEPT d ON u.DEPT_ID = d.DEPT_ID
        LEFT JOIN (
            SELECT 
                cs.GROUP_ID,
                COUNT(*) as student_count
            FROM DC_COURSE_STUDENT cs
            GROUP BY cs.GROUP_ID
        ) cs_count ON cg.GROUP_ID = cs_count.GROUP_ID
        WHERE c.CLASS_CODE = #{classCode}
        ORDER BY cg.GROUP_ORDER ASC, cg.CREATE_TIME ASC
    </select>

</mapper> 