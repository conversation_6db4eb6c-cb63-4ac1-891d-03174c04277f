<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.scenario.mapper.DcSceneStageMapper">
    
    <resultMap type="DcSceneStage" id="DcSceneStageResult">
        <result property="sceneStageId"    column="scene_stage_id"    />
        <result property="sceneId"    column="scene_id"    />
        <result property="sceneStageTitle"    column="scene_stage_title"    />
        <result property="sceneStageText"    column="scene_stage_text"    />
        <result property="sceneStageOrder"    column="scene_stage_order"    />
        <result property="createUser"    column="create_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="modifyUser"    column="modify_user"    />
        <result property="modifyTime"    column="modify_time"    />
        <association property="sceneAnnex"    javaType="DcSceneStageAnnex"         resultMap="annexResult" />
    </resultMap>


    <resultMap id="annexResult" type="DcSceneStageAnnex">
        <id     property="annexId"       column="annex_id"      />
        <result property="stageId"     column="stage_id"    />
        <result property="annexName"     column="annex_name"    />
        <result property="annexPath"     column="annex_path"    />
    </resultMap>

    <sql id="selectDcSceneStageVo">
        select scene_stage_id, scene_id, scene_stage_title, scene_stage_text, scene_stage_order, create_user, create_time, modify_user, modify_time from dc_scene_stage
    </sql>

    
    <select id="selectDcSceneStageBySceneStageId" parameterType="String" resultMap="DcSceneStageResult">
        <include refid="selectDcSceneStageVo"/>
        where scene_stage_id = #{sceneStageId}
    </select>

    <insert id="insertDcSceneStage" parameterType="DcSceneStage" useGeneratedKeys="true" keyProperty="sceneStageId">
        insert into dc_scene_stage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sceneStageId != null">SCENE_STAGE_ID,</if>
            <if test="sceneId != null">scene_id,</if>
            <if test="sceneStageTitle != null">scene_stage_title,</if>
            <if test="sceneStageText != null">scene_stage_text,</if>
            <if test="sceneStageOrder != null">scene_stage_order,</if>
            <if test="createUser != null">create_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="modifyUser != null">modify_user,</if>
            <if test="modifyTime != null">modify_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sceneStageId != null">#{sceneStageId},</if>
            <if test="sceneId != null">#{sceneId},</if>
            <if test="sceneStageTitle != null">#{sceneStageTitle},</if>
            <if test="sceneStageText != null">#{sceneStageText},</if>
            <if test="sceneStageOrder != null">#{sceneStageOrder},</if>
            <if test="createUser != null">#{createUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="modifyUser != null">#{modifyUser},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
         </trim>
    </insert>

    <update id="updateDcSceneStage" parameterType="DcSceneStage">
        update dc_scene_stage
        <trim prefix="SET" suffixOverrides=",">
            <if test="sceneId != null">scene_id = #{sceneId},</if>
            <if test="sceneStageTitle != null">scene_stage_title = #{sceneStageTitle},</if>
            <if test="sceneStageText != null">scene_stage_text = #{sceneStageText},</if>
            <if test="sceneStageOrder != null">scene_stage_order = #{sceneStageOrder},</if>
            <if test="createUser != null">create_user = #{createUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="modifyUser != null">modify_user = #{modifyUser},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
        </trim>
        where scene_stage_id = #{sceneStageId}
    </update>

    <delete id="deleteDcSceneStageBySceneStageId" parameterType="String">
        delete from dc_scene_stage where scene_stage_id = #{sceneStageId}
    </delete>

    <delete id="deleteDcSceneStageBySceneStageIds" parameterType="String">
        delete from dc_scene_stage where scene_stage_id in 
        <foreach item="sceneStageId" collection="array" open="(" separator="," close=")">
            #{sceneStageId}
        </foreach>
    </delete>
</mapper>