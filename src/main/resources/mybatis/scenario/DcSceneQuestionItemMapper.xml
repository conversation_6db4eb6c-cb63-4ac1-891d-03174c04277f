<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.scenario.mapper.DcSceneQuestionItemMapper">

    <update id="updateDcSceneQuestionItemIsRight">
        update
            DC_SCENE_QUESTION_ITEM
        set is_right ='N'
        where QUESTION_ID=  (select QUESTION_ID from DC_SCENE_QUESTION_ITEM where ITEM_ID =#{itemId}) ;
        update DC_SCENE_QUESTION_ITEM set is_right ='Y' where ITEM_ID =#{itemId};
    </update>
</mapper>