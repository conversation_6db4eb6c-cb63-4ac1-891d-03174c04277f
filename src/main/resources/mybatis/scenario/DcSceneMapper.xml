<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.scenario.mapper.DcSceneMapper">
    
    <resultMap type="DcScene" id="DcSceneResult">
        <result property="sceneId"    column="scene_id"    />
        <result property="sceneName"    column="scene_name"    />
        <result property="sceneIntroduction"    column="scene_introduction"    />
        <result property="sceneUserIdnumber"    column="scene_user_idnumber"    />
        <result property="sceneIspublic"    column="scene_ispublic"    />
        <result property="createUser"    column="create_user"    />
        <result property="createTime"    column="create_time"    />
        <result property="modifyUser"    column="modify_user"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="sceneTagid"    column="scene_tagid"    />
        <result property="sceneImage"    column="scene_image"    />
        <association property="user"    javaType="SysUser"         resultMap="userResult" />
    </resultMap>

    <resultMap id="userResult" type="SysUser">
        <id     property="userId"       column="user_id"      />
        <result property="userName"     column="user_name"    />
        <result property="nickName"     column="nick_name"    />
    </resultMap>

    <sql id="selectDcSceneVo">
        select ds.scene_id,
               ds.scene_name,
               ds.scene_introduction,
               ds.scene_user_idnumber,
               ds.scene_ispublic,
               ds.create_user,
               ds.create_time,
               ds.modify_user,
               ds.modify_time ,
               ds.scene_tagid,
               ds.scene_image,
               su.user_id,su.user_name,su.nick_name
        from dc_scene ds
        left join sys_user su on su.user_id = ds.scene_user_idnumber
    </sql>

    <select id="selectDcSceneList" parameterType="DcScene" resultMap="DcSceneResult">
        <include refid="selectDcSceneVo"/>
        <where>
            and isdelete != '1'
            <if test="sceneTagid != null  and sceneTagid != ''">
            and  ds.scene_tagid = #{sceneTagid}
            </if>
            and ds.create_user = #{createUser}
            <if test="sceneName != null  and sceneName != ''"> and ds.scene_name like concat('%', #{sceneName}, '%')</if>
            <if test="sceneIntroduction != null  and sceneIntroduction != ''"> and ds.scene_introduction  like concat('%', #{sceneIntroduction}, '%')</if>
            <if test="qUserName != null  and qUserName != ''"> and su.user_name like concat('%', #{qUserName}, '%')</if>
            <if test="sceneIspublic != null  and sceneIspublic != ''"> and ds.scene_ispublic = #{sceneIspublic}</if>
            <if test="modifyUser != null  and modifyUser != ''"> and ds.modify_user = #{modifyUser}</if>
            <if test="modifyTime != null "> and ds.modify_time = #{modifyTime}</if>
        </where>
    </select>
    
    <select id="selectDcSceneBySceneId" parameterType="String" resultMap="DcSceneResult">
        <include refid="selectDcSceneVo"/>
        where ds.scene_id = #{sceneId}
    </select>

    <select id="selectGetTagsByUser" resultType="map">
        SELECT TAG_ID,
               TAG_NAME,
               TAG_INDEX,
               NVL(T1.SCENE_COUNT,0)SCENE_COUNT
        FROM DC_SCENE_TAG T
                 LEFT JOIN (SELECT COUNT(*) SCENE_COUNT,
                                   SCENE_TAGID
                            FROM DC_SCENE
                            WHERE ISDELETE ='0'
                            GROUP BY SCENE_TAGID) T1
                           ON T.TAG_ID = T1.SCENE_TAGID
        WHERE CREATE_USER =#{userId}
        ORDER BY TAG_INDEX
    </select>

    <update id="updateTag">
        update DC_SCENE_TAG set TAG_NAME = #{TAG_NAME} ,TAG_INDEX = #{TAG_INDEX},modify_user=#{USERID},modify_time=SYSDATE() where TAG_ID = #{TAG_ID}
    </update>

    <delete id="delTag">
        delete from DC_SCENE_TAG where TAG_ID = #{tagid}
    </delete>

    <insert id="addTag">
        insert into DC_SCENE_TAG(TAG_ID,TAG_NAME,TAG_INDEX,create_user,create_time) values (#{TAG_ID},#{TAG_NAME},#{TAG_INDEX},#{CREATE_USER},SYSDATE())
    </insert>
</mapper>