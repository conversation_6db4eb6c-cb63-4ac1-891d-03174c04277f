<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.project.scenario.mapper.DcSceneQuestionMapper">
    <select id="getLeadQuestion" parameterType="String" resultType="com.ruoyi.project.scenario.domain.DcSceneQuestion">
        <include refid="selectDcSceneQuestionVo"/>
        where question_id =
        (
        SELECT NEXT_QUESTION_ID FROM
            (SELECT QUESTION_ID，
            LEAD(QUESTION_ID) OVER (ORDER BY QUESTION_INDEX) NEXT_QUESTION_ID
            FROM DC_SCENE_QUESTION where paper_id = #{paperId})
        where question_id = #{questionId})
    </select>

    <sql id="selectDcSceneQuestionVo">
        select question_index, question_id, queston_text, question_type, paper_id, question_answer, question_point, create_user, create_time, modify_user, modify_time from dc_scene_question
    </sql>
    <select id="getLagQuestion" parameterType="String" resultType="com.ruoyi.project.scenario.domain.DcSceneQuestion">
        <include refid="selectDcSceneQuestionVo"/>
        where question_id =
        (
        SELECT PREV_QUESTION_ID FROM
        (SELECT QUESTION_ID，
        LAG(QUESTION_ID) OVER (ORDER BY QUESTION_INDEX) PREV_QUESTION_ID
        FROM DC_SCENE_QUESTION where paper_id = #{paperId})
        where question_id = #{questionId})
    </select>

</mapper>