import request from '@/utils/request'
import {getCache} from "@/api/monitor/cache.js";

// 查询场景列表
export function listScene(query) {
  return request({
    url: '/scenario/scene/list',
    method: 'get',
    params: query
  })
}

export function listPuppetIco(query) {
  return request({
    url: '/scenario/scene/puppet/ico',
    method: 'get',
    params: query
  })
}
// 查询场景详细
export function getScene(sceneId) {
  return request({
    url: '/scenario/scene/' + sceneId,
    method: 'get'
  })
}

// 新增场景
export function addScene(data) {
  return request({
    url: '/scenario/scene',
    method: 'post',
    data: data
  })
}

export function tagsList(data) {
  return request({
    url: '/scenario/scene/tagslist',
    method: 'get',
    data: data
  })
}
export function editTag(data) {
  return request({
    url: '/scenario/scene/edittag',
    method: 'post',
    data: data
  })
}

// 修改场景
export function updateScene(data) {
  return request({
    url: '/scenario/scene',
    method: 'put',
    data: data
  })
}

// 删除场景
export function delScene(sceneId) {
  return request({
    url: '/scenario/scene/' + sceneId,
    method: 'delete'
  })
}

export function delTag(tagId) {
  return request({
    url: '/scenario/scene/tag/' + tagId,
    method: 'delete'
  })
}
