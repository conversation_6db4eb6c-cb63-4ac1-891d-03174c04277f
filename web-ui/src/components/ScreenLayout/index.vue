<template>
  <div class="screen-layout-page">
    <!-- 头部栏组件 -->
    <ScreenHeader />

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧面板 -->
      <div class="left-panel" :style="{ width: leftPanelWidth }">
        <div class="panel-header">
          <slot name="left-header">
            <el-icon v-if="leftDefaultIcon">
              <component :is="leftDefaultIcon" :color="iconColor" />
            </el-icon>
            <h3>{{ leftDefaultTitle }}</h3>
          </slot>
        </div>
        <div class="menu-list">
          <div 
            v-for="item in menuItems" 
            :key="getItemKey(item)"
            class="menu-item"
            :class="{ active: selectedItemKey === getItemKey(item) }"
            @click="handleItemSelect(item)">
            <slot name="menu-item" :item="item" :is-selected="selectedItemKey === getItemKey(item)">
              <div class="item-info">
                <div class="item-name">
                  <el-icon v-if="menuItemIcon">
                    <component :is="menuItemIcon" />
                  </el-icon>
                  <span>{{ getItemName(item) }}</span>
                </div>
              </div>
              <div class="item-checkbox" v-if="showCheckbox">
                <el-checkbox 
                  :model-value="selectedItems.includes(getItemKey(item))"
                  @update:model-value="(checked) => handleItemToggle(getItemKey(item), checked)"
                  @click.stop
                  class="custom-checkbox">
                </el-checkbox>
              </div>
            </slot>
          </div>
        </div>
      </div>

      <!-- 右侧面板 -->
      <div class="right-panel">
        <div class="panel-header">
          <slot name="right-header">
            <el-icon v-if="rightDefaultIcon">
              <component :is="rightDefaultIcon" />
            </el-icon>
            <h3>{{ rightDefaultTitle }}</h3>
            <slot name="right-header-extra"></slot>
          </slot>
        </div>
        <div class="content-area">
          <slot name="right-content">
            <!-- 默认无内容提示 -->
            <div v-if="showEmptyState" class="no-content">
              <i class="el-icon-chat-line-square"></i>
              <p>{{ emptyStateText }}</p>
            </div>
          </slot>
        </div>
      </div>
    </div>

    <!-- 悬浮按钮 -->
    <slot name="fab">
      <ScreenFab @fab-click="handleFabClick" />
    </slot>
  </div>
</template>

<script setup name="ScreenLayout">
import { ref, computed } from 'vue'
import ScreenHeader from '@/components/ScreenHeader/index.vue'
import ScreenFab from '@/components/ScreenFab/index.vue'

// Props 定义
const props = defineProps({
  // 左侧面板配置
  leftPanelWidth: {
    type: String,
    default: '280px'
  },
  leftDefaultIcon: {
    type: String,
    default: ''
  },
  leftDefaultTitle: {
    type: String,
    default: '菜单列表'
  },
  
  // 右侧面板配置
  rightDefaultIcon: {
    type: String,
    default: ''
  },
  rightDefaultTitle: {
    type: String,
    default: '内容区域'
  },
  
  // 菜单项配置
  menuItems: {
    type: Array,
    default: () => []
  },
  menuItemIcon: {
    type: String,
    default: ''
  },
  itemKeyField: {
    type: String,
    default: 'id'
  },
  itemNameField: {
    type: String,
    default: 'name'
  },
  
  // 选择功能
  showCheckbox: {
    type: Boolean,
    default: false
  },
  selectedItems: {
    type: Array,
    default: () => []
  },
  selectedItemKey: {
    type: [String, Number],
    default: ''
  },
  
  // 空状态配置
  showEmptyState: {
    type: Boolean,
    default: false
  },
  emptyStateText: {
    type: String,
    default: '暂无内容'
  },
  
  // 样式配置
  iconColor: {
    type: String,
    default: 'rgba(189, 4, 7, 1)'
  }
})

// Emits 定义
const emit = defineEmits([
  'item-select',
  'item-toggle',
  'fab-click'
])

// 获取项目的键值
const getItemKey = (item) => {
  return item[props.itemKeyField]
}

// 获取项目的名称
const getItemName = (item) => {
  return item[props.itemNameField]
}

// 处理项目选择
const handleItemSelect = (item) => {
  emit('item-select', item)
}

// 处理项目勾选切换
const handleItemToggle = (itemKey, checked) => {
  emit('item-toggle', itemKey, checked)
}

// 处理悬浮按钮点击
const handleFabClick = (item) => {
  emit('fab-click', item)
}
</script>

<style scoped>
.screen-layout-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  margin: 0 auto;
  gap: 0px;
  flex-direction: row;
  width: 100vw;
  height: calc(100vh - 64px);
  background: url('@/assets/images/screen/entry-background.png') no-repeat 100% 100%;
}

/* 左侧面板 */
.left-panel {
  background: white;
  border-radius: 0px;
  box-shadow: 0;
  overflow: hidden;
}

.panel-header {
  padding: 15px 20px;
  display: flex;
  justify-content: flex-start;
  gap: 8px;
  align-items: center;
  font-weight: bolder;
  font-size: 22px;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.menu-list {
  height: calc(100% - 62px);
  overflow-y: auto;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.menu-item:hover {
  background-color: #F8E5E6;
}

.menu-item:hover .item-name {
  color: rgba(189, 4, 7, 1);
}

.menu-item.active {
  /* background-color: #F8E5E6; */
  /* border-left: 3px solid rgba(189, 4, 7, 1); */
}

.item-info {
  flex: 1;
}

.item-name {
  font-weight: normal;
  color: #333;
  margin-bottom: 4px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 6px;
}

.item-detail {
  font-size: 12px;
  color: #666;
}

.item-checkbox {
  margin-left: 12px;
}

/* 自定义复选框样式 */
.custom-checkbox :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: rgba(189, 4, 7, 1);
  border-color: rgba(189, 4, 7, 1);
}

.custom-checkbox :deep(.el-checkbox__inner:hover) {
  border-color: rgba(189, 4, 7, 1);
}

/* 右侧面板 */
.right-panel {
  flex: 1;
  /* background: #F3F3F3; */
  border-radius: 0;
  box-shadow: 0;
  overflow: hidden;
  display: flex; /* 使用 flex 布局 */
  flex-direction: column; /* 垂直排列 */
}

.content-area {
  flex: 1; /* 占据剩余空间 */
  overflow-y: auto;
  background-color: #Ffffff;
  margin: 0 20px 20px 20px;
  padding: 20px;
  border-radius: 8px;
}

.no-content {
  text-align: center;
  color: #999;
  padding: 60px 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.no-content i {
  font-size: 64px;
  margin-bottom: 16px;
  color: #ddd;
}

.no-content p {
  margin: 0;
  font-size: 16px;
}

/* 自定义按钮样式 */
.custom-button {
  color: rgba(189, 4, 7, 1) !important;
}

.custom-button:hover {
  color: rgba(189, 4, 7, 0.8) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
    padding: 10px;
  }
  
  .left-panel {
    width: 100% !important;
    margin-bottom: 20px;
  }
}
</style> 