#app {

  .main-container {
    min-height: 100%;
    transition: margin-left .28s;
    margin-left: $base-sidebar-width;
    position: relative;
  }

  .sidebarHide {
    margin-left: 0!important;
  }

  .sidebar-container {
    transition: width 0.28s;
    width: $base-sidebar-width !important;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    -webkit-box-shadow: 2px 0 6px rgba(0,21,41,.35);
    box-shadow: 2px 0 6px rgba(0,21,41,.35);

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }

    .el-menu-item, .menu-title {
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
    }

    .el-menu-item .el-menu-tooltip__trigger {
      display: inline-block !important;
    }

    // menu hover
    .sub-menu-title-noDropdown,
    .el-sub-menu__title {
      &:hover {
        background-color: rgba(0, 0, 0, 0.06) !important;
      }
    }

    & .theme-dark .is-active > .el-sub-menu__title {
      color: $base-menu-color-active !important;
    }

    & .nest-menu .el-sub-menu>.el-sub-menu__title,
    & .el-sub-menu .el-menu-item {
      min-width: $base-sidebar-width !important;

      &:hover {
        background-color: rgba(0, 0, 0, 0.06) !important;
      }
    }

    & .theme-dark .nest-menu .el-sub-menu>.el-sub-menu__title,
    & .theme-dark .el-sub-menu .el-menu-item {
      background-color: $base-sub-menu-background;

      &:hover {
        background-color: $base-sub-menu-hover !important;
      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 54px !important;
    }

    .main-container {
      margin-left: 54px;
    }

    .sub-menu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }
      }
    }

    .el-sub-menu {
      overflow: hidden;

      &>.el-sub-menu__title {
        padding: 0 !important;

        .svg-icon {
          margin-left: 20px;
        }

      }
    }

    .el-menu--collapse {
      .el-sub-menu {
        &>.el-sub-menu__title {
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
          &>i {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu--collapse .el-menu .el-sub-menu {
    min-width: $base-sidebar-width !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform .28s;
      width: $base-sidebar-width !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$base-sidebar-width, 0, 0);
      }
    }
  }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 16px;
    }
  }

  .nest-menu .el-sub-menu>.el-sub-menu__title,
  .el-menu-item {
    &:hover {
      // you can use $sub-menuHover
      background-color: rgba(0, 0, 0, 0.06) !important;
    }
  }

  // the scroll bar appears when the sub-menu is too long
  >.el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}

// 导入主题变量
@import './element-variables.scss';

.sidebar-container {
  // 主导航栏背景使用稍浅的红色
  background-color: $el-color-primary-light-3 !important; 
  padding: 0;
}

.sidebar-container .el-menu {
  // 菜单背景与导航栏一致
  background-color: $el-color-primary-light-3 !important; 
  border-right: none;

  // 菜单项和子菜单标题样式
  .el-menu-item,
  .el-sub-menu__title {
    color: $el-button-text-color; // 设置文字颜色为白色
    background-color: transparent !important;

    &:hover {
      // 悬停时使用更深的红色
      background-color: $el-color-primary !important; 
    }
  }

  // 激活状态菜单项样式
  .el-menu-item.is-active {
    // 激活状态使用最深的红色
    background-color: $el-color-primary-dark-2 !important; 
  }
}

// 子菜单样式
.sidebar-container .el-sub-menu .el-menu {
  // 子菜单背景使用比主菜单稍深的红色
  background-color: $el-color-primary !important; 

  .el-menu-item {
    &:hover {
      // 子菜单悬停时使用合适的红色，避免暗色
      background-color: $el-color-primary-light-3 !important; 
      color: $el-button-text-color; // 确保文字颜色为白色
    }
  }
}

// 处理嵌套子菜单标题悬停样式
.sidebar-container .nest-menu .el-sub-menu>.el-sub-menu__title {
  &:hover {
    background-color: $el-color-primary-light-3 !important; 
    color: $el-button-text-color; 
  }
}

// 侧边栏折叠状态样式
.sidebar-container.el-menu--collapse {
  // 折叠状态使用稍浅的红色
  background-color: $el-color-primary-light-3 !important; 
}

// 解决可能存在的嵌套菜单背景问题
.sidebar-container .nest-menu .el-sub-menu>.el-sub-menu__title,
#app .sidebar-container .theme-dark .el-sub-menu .el-menu-item {
  &:hover {
    // 用更浅的红色，增强对比度
    background-color: $el-color-primary-light-3 !important; 
    color: $el-button-text-color !important; 
  }
}

#app .sidebar-container .theme-dark .nest-menu .el-sub-menu>.el-sub-menu__title {
  &:hover {
    // 嵌套子菜单标题悬停也用同样的红色
    background-color: $el-color-primary-light-3 !important; 
    color: $el-button-text-color !important; 
  }
}

// 强化 .is-active 状态下子菜单标题的悬停样式
#app .sidebar-container .theme-dark .is-active > .el-sub-menu__title {
  &:hover {
    background-color: $el-color-primary-light-3 !important; 
    color: $el-button-text-color !important; 
  }
}

// 侧边栏折叠状态样式
.sidebar-container.el-menu--collapse {
  // 折叠状态使用稍浅的红色
  background-color: $el-color-primary-light-3 !important; 
}

// 解决可能存在的嵌套菜单背景问题
.sidebar-container .nest-menu .el-sub-menu>.el-sub-menu__title,
.sidebar-container .el-sub-menu .el-menu-item {
  background-color: inherit !important;
}
