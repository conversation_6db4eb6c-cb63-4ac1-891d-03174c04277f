import { createWebHistory, createRouter } from 'vue-router'
/* Layout */
import Layout from '@/layout'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index.vue')
      }
    ]
  },
// 测试 屏幕同步使用  2025-06-08
  // {
  //   path: '/screen',
  //   component: () => import('@/views/screen/a.vue'),
  // },
  //  {
  //   path: '/screenb',
  //   component: () => import('@/views/screen/b.vue'),
  // },
  //  {
  //   path: '/screenc',
  //   component: () => import('@/views/screen/c.vue'),
  // },
// 教师屏幕控制端2025-06-08
    {
    path: '/screen/teacher',
    name: 'TeacherShow',
    component: () => import('@/views/screen/teacher_show.vue'),
    hidden: true,
     redirect: { name: 'CourseStages1' },
    children: [
        {
          path: 'stages',
          component: () => import('@/views/screen/stages/index.vue'),
          name: 'CourseStages1',
          meta: { title: '课程阶段', icon: 'operation' },
          hidden: true
        },
         {
            path: 'monitor',
            component: () => import('@/views/screen/monitor/index.vue'),
            name: 'ChatMonitor1',
            meta: { title: '聊天监控管理', icon: 'monitor' },
            hidden: true
          },
            {
          path: 'survey',
          component: () => import('@/views/screen/survey/index.vue'),
          name: 'SurveyResult1',
          meta: { title: '问卷统计', icon: 'document' },
          hidden: true
        },
    ]
    },
    // 大屏显示
     {
    path: '/screen/max',
    name: 'MaxShow',
    component: () => import('@/views/screen/max_show.vue'),
    hidden: true,
     redirect: { name: 'CourseStages2' },
     meta: { title: '大屏显示', requiresAuth: false  },
    children: [
        {
          path: 'stages',
          component: () => import('@/views/screen/stages/index.vue'),
          name: 'CourseStages2',
          meta: { title: '课程阶段', icon: 'operation', requiresAuth: false },
          hidden: true
        },
         {
            path: 'monitor',
            component: () => import('@/views/screen/monitor/index.vue'),
            name: 'ChatMonitor2',
            meta: { title: '聊天监控管理', icon: 'monitor', requiresAuth: false },
            hidden: true
          },
            {
          path: 'survey',
          component: () => import('@/views/screen/survey/index.vue'),
          name: 'SurveyResult2',
          meta: { title: '问卷统计', icon: 'document', requiresAuth: false },
          hidden: true
        },
    ]
    },
  // 课程码输入页面
  {
    path: '/screen/entry',
    component: () => import('@/views/screen/entry/index.vue'),
    name: 'CourseCodeEntry',
    meta: { title: '进入大屏', icon: 'key',requiresAuth: false },
    hidden: false
  },
  // 聊天监控管理页面
  {
    path: '/screen/monitor',
    component: () => import('@/views/screen/monitor/index.vue'),
    name: 'ChatMonitor',
    meta: { title: '聊天监控管理', icon: 'monitor' },
    hidden: true
  },
  // 问卷统计页面
  {
    path: '/screen/survey',
    component: () => import('@/views/screen/survey/index.vue'),
    name: 'SurveyResult',
    meta: { title: '问卷统计', icon: 'document' },
    hidden: true
  },
  // 课程阶段页面
  {
    path: '/screen/stages',
    component: () => import('@/views/screen/stages/index.vue'),
    name: 'CourseStages',
    meta: { title: '课程阶段', icon: 'operation' },
    hidden: true
  },

// -----------------------------------------------
    //--------------以下是教师端来的路由--------------
  {
    path: '/scenario/scene',
    component: () => import('@/views/scenario/scene/index.vue'),
    name: 'Scene',
    meta: { title: '场景管理', icon: 'operation' },
    hidden: true
  },
  {
    path: '/scenario/scene-info/:sceneId',
    component: () => import('@/views/scenario/scene/info'),
    name: 'SceneInfo',
    meta: { title: '场景信息' },
    hidden: true
  },
  {
    path: '/scenario/course',
    component: () => import('@/views/scenario/course/index.vue'),
    name: 'Course',
    meta: { title: '课程管理', icon: 'operation' },
    hidden: true
  },
  {
    path: '/scenario/course-info/:courseId',
    component: () => import('@/views/scenario/course/info'),
    name: 'CourseInfo',
    meta: { title: '课程信息' },
    hidden: true
  },

    //--------------教师端来路由结束-----------------
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true
  },
  {
    path: "/:pathMatch(.*)*",
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    redirect: '/index',
    children: [
      {
        path: '/index',
        component: () => import('@/views/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: () => import('@/views/system/user/profile/index'),
        name: 'Profile',
        meta: { title: '个人中心', icon: 'user' }
      }
    ]
  }
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:user:edit'],
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: () => import('@/views/system/user/authRole'),
        name: 'AuthRole',
        meta: { title: '分配角色', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    permissions: ['system:role:edit'],
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: () => import('@/views/system/role/authUser'),
        name: 'AuthUser',
        meta: { title: '分配用户', activeMenu: '/system/role' }
      }
    ]
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    permissions: ['system:dict:list'],
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: () => import('@/views/system/dict/data'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict' }
      }
    ]
  },
  {
    path: '/monitor/job-log',
    component: Layout,
    hidden: true,
    permissions: ['monitor:job:list'],
    children: [
      {
        path: 'index/:jobId(\\d+)',
        component: () => import('@/views/monitor/job/log'),
        name: 'JobLog',
        meta: { title: '调度日志', activeMenu: '/monitor/job' }
      }
    ]
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    permissions: ['tool:gen:edit'],
    children: [
      {
        path: 'index/:tableId(\\d+)',
        component: () => import('@/views/tool/gen/editTable'),
        name: 'GenEdit',
        meta: { title: '修改生成配置', activeMenu: '/tool/gen' }
      }
    ]
  }
  // ,
  // {
  //   path: '/scenario/course-info',
  //   component: Layout,
  //   hidden: true,
  //   permissions: ['scenario:scene:list'],
  //   children: [
  //     {
  //       path: 'index/:courseId',
  //       component: () => import('@/views/scenario/course/info'),
  //       name: 'CourseInfo',
  //       meta: { title: '课程信息', activeMenu: '/scenario/course/info' }
  //     }
  //   ]
  // }
]

const router = createRouter({
  history: createWebHistory(),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    return { top: 0 }
  },
});

export default router;
