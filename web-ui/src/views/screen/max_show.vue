<template>
    <div style="width: 100%; height: 100%;">
          <div class="screen-c">
            <p v-if="serverState.connected">Connected to server!</p>
            <p v-if="serverState.error">{{ serverState.error }}</p>
            <p>Device ID: {{ deviceId }}；Screen:{{ windowWidth }},{{ windowHeight }}</p>
               <p>{{ state.lastMousePosition.x }},{{ state.lastMousePosition.y }}----{{ state.lastMousePosition.percentX}},{{ state.lastMousePosition.percentY }}</p>
        </div>

        
        <div class="point" :style="'left:'+ state.lastMousePosition.x +'px; top:'+ state.lastMousePosition.y +'px;'">

        </div>
        <router-view></router-view>
    </div>
</template>
<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRoute } from 'vue-router'
// 路由实例
const route = useRoute()
// 设备ID
const deviceId = ref(crypto.randomUUID());
console.log('Device ID:', deviceId.value);
// 最终的 状态记录
const state = reactive({
  clickCount: 0,
  lastMousePosition: {
    x: 0,
    y: 0,
    percentX:0,
    percentY:0
  },
  scrollTop: 0
});

const serverState=ref({
    connected: false,
    error: null
});
onMounted(() => {
    connectServer();
 window.addEventListener('resize', handleResize);
});

// 窗口大小处理
const windowWidth = ref(window.innerWidth);
const windowHeight = ref(window.innerHeight);

// 处理窗口大小变化的函数
const handleResize = () => {
  windowWidth.value = window.innerWidth;
  windowHeight.value = window.innerHeight;
};

let ws=null;
const code= route.query.code;
const connectServer = () => {
    
    ws= new WebSocket('ws://localhost:8080/ws/screen/' + code );
    ws.onopen = () => {
        console.log('WebSocket connection established:'+code);
        serverState.value.connected = true;
    };
    ws.onmessage = (event) => {
        // console.log('Message from server:', event.data);
        // 这里可以处理从服务器接收到的消息
        let json= JSON.parse(event.data);
        // console.log('Received message deviceId:', json.deviceId,'----',deviceId.value);
    
            if(json.type=='mousemove'){

                 // 获取屏幕的实际分辨率（宽高）
               
                let lastx=json.data.clientX*windowWidth.value/100;
                let lasty=json.data.clientY*windowHeight.value/100;
                state.lastMousePosition = {
                    x: Math.round(lastx),
                    y: Math.round(lasty),
                    percentX: json.data.clientX,
                    percentY: json.data.clientY
                };
            } else if (json.type == 'click') {
                console.log('Click event received:', json.data);
                 const screenWidth = window.screen.width;
                 const screenHeight = window.screen.height;
                if(json.data.targetId){
                    // 如果有targetId,则点击指定元素
                    document.getElementById(json.data.targetId)?.click();
                }else{
                document.elementFromPoint(json.data.clientX*screenWidth, json.data.clientY*screenHeight)?.click();
                }
                state.clickCount++;
            } else if (json.type === 'scroll') {
                state.scrollTop = json.data.scrollTop;
                window.scrollTo(0, state.scrollTop);
            }
        
    };
    ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        serverState.value.error = 'WebSocket error: ' + error.message;
    };
};

</script>

<style scoped>
.point{
    width: 10px;
    height: 10px;
    background-color: red;
    position: absolute;
    border-radius: 50%;
}
</style>