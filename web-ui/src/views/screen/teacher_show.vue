<template>
    <div style="width: 100%; height: 100%;" @mousemove="handleMouseMove">
          <div class="screen-a">
            <p v-if="serverState.connected">Connected to server!</p>
            <p v-if="serverState.error">{{ serverState.error }}</p>
            <p>Device ID: {{ deviceId }}；Screen:{{ windowWidth }},{{ windowHeight }}</p>
            <p>{{ state.lastMousePosition.x }},{{ state.lastMousePosition.y }}----{{ state.lastMousePosition.percentX}},{{ state.lastMousePosition.percentY }}</p>
        </div>

        
        <div class="point" :style="'left:'+ state.lastMousePosition.x +'px; top:'+ state.lastMousePosition.y +'px;'">

        </div>
       <router-view/>
    </div>
</template>
<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
// 路由实例
const route = useRoute()
// 设备ID
const deviceId = ref(crypto.randomUUID());
console.log('Device ID:', deviceId.value);
// 最终的 状态记录
const state = reactive({
  clickCount: 0,
  lastMousePosition: {
    x: 0,
    y: 0,
    percentX:0,
    percentY:0
  },
  scrollTop: 0,
 
});

const serverState=ref({
    connected: false,
    error: null
});

onMounted(() => {
    connectServer();
    window.addEventListener('resize', handleResize);
    window.addEventListener('click', handleClick);
    window.addEventListener('scroll', handleMouseMove);
});

// 窗口大小处理
const windowWidth = ref(window.innerWidth);
const windowHeight = ref(window.innerHeight);

// 处理窗口大小变化的函数
const handleResize = () => {
  windowWidth.value = window.innerWidth;
  windowHeight.value = window.innerHeight;
};



let ws=null;
const code= route.query.code;
const connectServer = () => {
    
    ws= new WebSocket('ws://localhost:8080/ws/screen/' + code );
    ws.onopen = () => {
          console.log('WebSocket connection established:'+code);
          serverState.value.connected = true;
    };
    ws.onmessage = (event) => {
        // console.log('Message from server:', event.data);
        // 这里可以处理从服务器接收到的消息
        let json= JSON.parse(event.data);
        // console.log('Received message deviceId:', json.deviceId,'----',deviceId.value);
        if(json.deviceId !== deviceId.value){
            if(json.type=='mousemove'){
                state.lastMousePosition = {
                    x: Math.round(json.data.clientX),
                    y: Math.round(json.data.clientY)
                };
            } else if (json.type == 'click') {
                console.log('Click event received:', json.data);
                document.elementFromPoint(json.data.clientX, json.data.clientY)?.click();
                state.clickCount++;
            } else if (json.type === 'scroll') {
                state.scrollTop = json.data.scrollTop;
            }
        }
    };
    ws.onerror = (error) => {
         ElMessage.error('WebSocket error:'+ error)
        console.error('WebSocket error:', error);
        serverState.value.error = 'WebSocket error: ' + error.message;
    };
};

let mouseMoveTimer = null;
// 鼠标移动
const handleMouseMove = (event) => {
  // 节流处理，每100ms发送一次
  if (!mouseMoveTimer) {
    mouseMoveTimer = setTimeout(() => {
     

      const screenX = event.clientX; // 相对于屏幕左侧的水平坐标
      const screenY = event.clientY; // 相对于屏幕顶部的垂直坐标
      // 计算百分比（四舍五入保留两位小数）
      const percentX = ((screenX / windowWidth.value) * 100).toFixed(2);
      const percentY = ((screenY / windowHeight.value) * 100).toFixed(2);

       state.lastMousePosition = {
        x: Math.round(event.clientX),
        y: Math.round(event.clientY),
        percentX: percentX,
        percentY: percentY
      };


      let msg= {
        type: 'mousemove',
        deviceId: deviceId.value,
        data: {
          clientX: percentX,
          clientY: percentY
        }
      };
      sendSocketMessage(msg);
      mouseMoveTimer = null;
    }, 100);
  }
};
// 鼠标点击
const handleClick = (event) => {
       state.clickCount++;
      const screenX = event.clientX; // 相对于屏幕左侧的水平坐标
      const screenY = event.clientY; // 相对于屏幕顶部的垂直坐标

      // 计算百分比（四舍五入保留两位小数）
      const percentX = ((screenX / windowWidth) * 100).toFixed(4);
      const percentY = ((screenY / windowHeight) * 100).toFixed(4);
      // event.target.id;
    let tid=event.target.id;

    let msg= {
        type: 'click',
        deviceId: deviceId.value,
        data: {
            clientX: percentX,
            clientY: percentY,
            targetId: tid,
        }
    };
    console.log('Click event:', msg);
    sendSocketMessage(msg);
};
// 监听窗口滚动事件
window.addEventListener('scroll', () => {
    state.scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
    let msg= {
        type: 'scroll',
        deviceId: deviceId.value,
        data: {
            scrollTop: state.scrollTop
        }
    };
    sendSocketMessage(msg);
});

 // 获取所有可滚动的 div 元素
const getScrollableDivs = () => {
    return Array.from(document.querySelectorAll('div')).filter(div => 
      div.scrollHeight > div.clientHeight || div.scrollWidth > div.clientWidth
    );
  };
    // 添加滚动监听器
const addScrollListeners = () => {
    scrollableDivs.value = getScrollableDivs();
    scrollableDivs.value.forEach(div => {
      div.addEventListener('scroll', handleScroll);
    });
  };


const sendSocketMessage = (message) => {
    if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(message));
    } else {
        ElMessage.error('WebSocket is not open. Unable to send message')
        console.error('WebSocket is not open. Unable to send message:', message);
    }
};

</script>

<style scoped>
.point{
    width: 10px;
    height: 10px;
    background-color: red;
    position: absolute;
    border-radius: 50%;
}
</style>