<template>
  <!-- 头部栏组件 -->
  <ScreenLayout :leftDefaultTitle="menuTitle" :menuItems="menuItems" :selectedItemKey="selectedItemKey" :rightDefaultTitle="title">
    <template #right-header-extra>
      <div style="margin-left: auto;height: 17px;">
        <el-button icon="Tools" class="red-btn"  @click="handleManageTag">分类管理</el-button>
      </div>
    </template>
    <template #right-content>
      <div>
        <div v-if="data.dynamicTags.length == 0" >
          <el-empty description="暂无数据"></el-empty>
        </div>
        <div class="tag-box" v-else v-for="(tag, index) in data.tags" :key="tag.tag_id" >
          <div style="display: flex;">
            <div style="margin-bottom: 17px;
                      margin-top: 17px;
                      font-weight: 700;
                      line-height: 16px;" >{{ tag.TAG_NAME }}</div>
            <div class="top-right-btn" style="display: flex; align-items: center;margin-right: 12px">
              <a style="color: #93979C;font-size: 14px;" v-if="!tag.openall && data.openTag.indexOf(index) < 0 " @click="handleOpenAll(index)">展开全部</a>
              <a style="color: #93979C;font-size: 14px;" v-else @click="handleOpenAll(index)">收起</a>
            </div>
          </div>
          <div class="tag-scene-box" v-if="sceneMap.get(tag.TAG_ID) && sceneMap.get(tag.TAG_ID).length === 0">
            <div class="scene-box" >
              <a @click="handleAddSceneForTag(tag)" class="add-scene">添加场景</a>
            </div>
          </div>
          <div :class="tag.openall || data.openTag.indexOf(index) >= 0 ?'tag-scene-box':'tag-scene-box close'" v-else>
            <div class="scene-box" v-for="(scene, index) in sceneMap.get(tag.TAG_ID)" :key="scene.scene_id">
              <div class="public" v-if="scene.sceneIspublic == 'Y'">公共</div>
              <div class="img_box" @click="handleOpenScene(scene)">
                <el-image class="bigImg" :src="baseUrl + scene.sceneImage">
                  <template #error>
<!--                    <div class="image-slot" >-->
<!--                      <el-icon><picture-filled /></el-icon>-->
<!--                    </div>-->
                    <img src="/src/assets/images/no-png.png" alt="">
                  </template>
                </el-image>
              </div>
              <a @click="handleOpenScene(scene)">{{scene.sceneName}}</a>
              <div style="display: flex;">
                <div>
                  <el-button link type="primary" icon="Edit" @click="handleEditSceneForTag(scene,tag)" >修改</el-button>
                  <el-button link type="primary" icon="Delete" @click="handleDelSceneForTag(scene,tag)" >删除</el-button>
                </div>
              </div>
            </div>
            <div class="scene-box" >
              <a @click="handleAddSceneForTag(tag)"
                 class="add-scene">添加场景</a>
            </div>
          </div>
        </div>


        <!-- 管理场景分类对话框 -->
        <el-dialog :title="tagTitle" v-model="tagOpen" width="500px" append-to-body>
          <el-form ref="sceneRef" :model="form" :rules="rules" label-width="80px">
            <div>
              <div style="margin: 0 0 10px 0;margin-left: 5px;">
                <el-button icon="Plus" @click="handleAddTagClick">新增</el-button>
                <span style="margin-left: 5px;">提示：点击分类名修改，拖拽分类排序</span>
              </div>
              <draggable v-model="data.dynamicTags" style="display: grid;grid-template-columns: 1fr 1fr;" >
                <template #item="{element,index}">
                  <div style="width: 200px;">
                    <el-input v-if="editable[index]" :ref="el => (inputEditRef[index] = el)" v-model="element.TAG_NAME" style="width: 200px;margin: 0 10px 5px 0;" placeholder="请输入分类名称" @blur="handleInputEditConfirm(element,index)" @change="handleInputEditChange(element, index)"/>
                    <el-tag v-else closable :disable-transitions="false" size="large" style="width: 200px;margin: 0 10px 5px 0;" @click="showEditTagInput(index)" @close="handleDelTag(element, index)" >
                      {{ element.TAG_NAME }}
                    </el-tag>
                  </div>
                </template>
              </draggable >
            </div>
          </el-form>
          <template #footer>
            <div class="dialog-footer">
              <el-button type="primary" @click="submitForm">确 定</el-button>
              <el-button @click="cancel">关 闭</el-button>
            </div>
          </template>
        </el-dialog>
        <!-- 添加或修改场景对话框 -->
        <el-dialog :title="sceneTitle" v-model="openScenceEdit" width="500px" append-to-body>
          <el-form ref="sceneRef" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="场景名称" prop="sceneName">
              <el-input v-model="form.sceneName" placeholder="请输入场景名称" />
            </el-form-item>
            <el-form-item label="公共场景" prop="sceneIspublic">
              <el-radio-group v-model="form.sceneIspublic">
                <el-radio v-for="dict in sys_yes_no" :key="dict.value" :value="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="场景图片" prop="sceneImage">
              <image-upload limit="1" v-model="form.sceneImage"/>
            </el-form-item>
            <el-form-item label="场景介绍" prop="sceneIntroduction">
              <el-input v-model="form.sceneIntroduction" placeholder="请输入场景介绍" type="textarea" />
            </el-form-item>
          </el-form>
          <template #footer>
            <div class="dialog-footer">
              <el-button type="primary" @click="submitSceneForm">确 定</el-button>
              <el-button @click="cancel">取 消</el-button>
            </div>
          </template>
        </el-dialog>
      </div>
    </template>
    <template  #fab>
      <div></div>
    </template>
  </ScreenLayout>

</template>

<script setup name="Scene">
import ScreenLayout from "@/components/ScreenLayout/index.vue";
import { listScene, getScene, delScene, addScene, updateScene,tagsList,editTag,delTag } from "@/api/scenario/scene"
import { deptUserTreeSelect } from "@/api/system/user";
import draggable from "vuedraggable";
import {delGroup, getGroup} from "@/api/scenario/group.js";
import {getToken} from "@/utils/auth.js";
import {ref} from "vue";
import Day from "@/components/Crontab/day.vue";

const title = ref("我的场景")
const menuTitle = ref("场景管理")
const menuItems = ref([
    { id: 1,name:"我的场景" },
    { id: 2,name:"我的课程" }
])
const selectedItemKey = ref(1)

const tagTitle = ref("")
const tagOpen = ref(false)

const router = useRouter();
const { proxy } = getCurrentInstance()
const { sys_yes_no } = proxy.useDict("sys_yes_no");

const sceneMap = ref(new Map());
// const sceneList = ref([])
const open = ref(false)
const openScenceEdit = ref(false)
const sceneTitle = ref("")

const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

 const tags = ref(undefined);
const enabledDeptOptions = ref(undefined)
// 上传文件列表
const field101fileList = ref([])
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API );
const field101 = ref(null);
const editable = ref([])
const inputEditRef = ref([])


const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    sceneName: null,
    sceneIntroduction: null,
    sceneUserIdnumber: null,
    sceneIspublic: null,
    sceneTagid: null
  },
  rules: {
    sceneName: [{ required: true, message: "场景名称不能为空", trigger: "blur" }],
    sceneIntroduction: [{ required: true, message: "场景介绍不能为空", trigger: "blur" }],
    sceneUserIdnumber: [{ required: true, message: "创建人不能为空", trigger: "blur" }],
    sceneIspublic: [{ required: true, message: "是否公共场景不能为空", trigger: "blur" }]
  },
  dynamicTags: [],
  tags: [],
  inputVisible: false,
  inputValue: '',
  tempTag: '',
  // 是否是重复数据
  isRepeatedData: false,
  // 是否改变原来的值
  isChange:  false,
  sceneId: '',
  openTag: []
})

const { queryParams, form, rules } = toRefs(data)

function cancel() {
  open.value = false
  tagOpen.value = false
  openScenceEdit.value = false
}

const handleAddSceneForTag = (tag) => {
  reset()
  openScenceEdit.value = true
  data.sceneId = tag.TAG_ID
  sceneTitle.value = "添加场景"
}

const handleEditSceneForTag = (scene,tag) => {
  reset()
  const sceneId = scene.sceneId
  getScene(sceneId).then(response => {
    form.value = response.data
    openScenceEdit.value = true
    sceneTitle.value = "修改场景"
  })
}

function handleDelSceneForTag(scene,tag) {
  const sceneId = scene.sceneId
  proxy.$modal.confirm('是否确认删除所选数据项？').then(function() {
    return delScene(sceneId)
  }).then(() => {
    getTags()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

const handleOpenScene = (scene,tag) => {
  debugger
  const sceneId = scene.sceneId
  router.push("/scenario/scene-info/" + sceneId );
}

// 表单重置
function reset() {
  form.value = {
    sceneId: null,
    sceneName: null,
    sceneIntroduction: null,
    sceneUserIdnumber: null,
    sceneIspublic: null,
    sceneImage: null,
    sceneTagid: null,
  }

  proxy.resetForm("sceneRef")
}
const handleOpenAll = (index) => {
  data.tags[index].openall = !data.tags[index].openall
  if(data.tags[index].openall){
    data.openTag.push(index)
  }else{
    data.openTag.splice(data.openTag.indexOf(index), 1);
  }
}
const handleAddTagClick = () => {
  const lastIndex = data.dynamicTags.length
  data.dynamicTags.push({TAG_ID: "",TAG_NAME: ""})
  editable.value[lastIndex] = true
  nextTick(() => {
    inputEditRef.value[lastIndex].input.focus()
  })
}

const handleInputEditChange = async (tag, index) => {
  editable.value[index] = false
  if (tag.name) {
    data.dynamicTags.push(tag);
    editable.value[index] = false
  }
}
const handleInputEditConfirm = (tag, index) => {
  editable.value[index] = false
}

//编辑标签信息 input显示
const showEditTagInput = index => {
  editable.value[index] = true
  nextTick(() => {
    inputEditRef.value[index].input.focus()
  })
}

function handleDelTag(tag,index) {
  proxy.$modal.confirm('是否确认删除？').then(function() {
    if(tag.TAG_ID){
      return delTag(tag.TAG_ID)
    }

  }).then(() => {
    data.dynamicTags.splice(index, 1);
    proxy.$modal.msgSuccess("删除成功")
    getTags()
  }).catch(() => {})
}


// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.sceneId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleManageTag() {
  tagOpen.value = true
  tagTitle.value = "管理场景分类"
}


/** 提交按钮 */
function submitForm() {
  let canSubmit = true
  if(data.dynamicTags.length == 0){
    proxy.$modal.msgError("分类不能为空")
    canSubmit = false
    return false
  }
  data.dynamicTags.forEach((item,index) => {
    data.dynamicTags[index].TAG_INDEX = index
    if(item.TAG_NAME === ''){
      proxy.$modal.msgError("分类不能为空")
      canSubmit = false
      return false
    }
  })
  if(canSubmit){
    editTag(data.dynamicTags).then(response => {
      proxy.$modal.msgSuccess("操作成功")
      cancel()
      getTags()
    })
  }
}

/** 提交按钮 */
function submitSceneForm() {
  proxy.$refs["sceneRef"].validate(valid => {
    if (valid) {
      form.value.sceneTagid = data.sceneId
      if (form.value.sceneId != null) {
        updateScene(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          openScenceEdit.value = false
          getTags()
        })
      } else {
        addScene(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          openScenceEdit.value = false
          getTags()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _sceneIds = row.sceneId || ids.value
  proxy.$modal.confirm('是否确认删除所选数据项？').then(function() {
    return delScene(_sceneIds)
  }).then(() => {
    getTags()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}



function getTags(){
  tagsList().then(response => {
    data.tags = JSON.parse(JSON.stringify(response));
    data.dynamicTags = JSON.parse(JSON.stringify(response));
    data.tags.forEach(item =>{
      listScene({sceneTagid: item.TAG_ID}).then(response =>{
        sceneMap.value.set(item.TAG_ID,JSON.parse(JSON.stringify(response.data)))
      })
    })
  });
}
/** 过滤禁用的部门 */
function filterDisabledDept(deptList) {
  return deptList.filter(dept => {
    if (dept.disabled) {
      return false;
    }
    if (dept.children && dept.children.length) {
      dept.children = filterDisabledDept(dept.children);
    }
    return true;
  });
}

getTags()

</script>

<style>
.content-area {
  background-color: rgba(0, 0, 0, 0) !important;
  padding: 0 !important;
  border-radius: 0 !important;
}
</style>

<style scoped>
/* 局部样式 */

.tag-box {
  padding-left: 20px;
  margin-bottom: 10px;
  background-color: #ffffff;
}
.add-scene {
  height: 200px;
  width: 300px;
  float: left;
  border: 1px dashed rgba(221, 221, 221, 0.7);
  padding: 110px 0 0;
  line-height: 20px;
  font-size: 12px;
  text-align: center;
  color: #cbcbcb;
  background-position: center 70px;
  background-repeat: no-repeat;
  background-image: url(/src/assets/images/add.png);
  border-radius: 6px;
}

.tag-scene-box.close {
  overflow: hidden;
  height: 230px;
}
.tag-scene-box {
  flex-wrap: wrap;
  display: flex;
  width: calc(100% - 20px);
}
.tag-scene-box > .scene-box {
  position: relative;
  height: 230px;
  width: 320px;
  text-align: center;
  padding-right: 20px;
}

.tag-scene-box > .scene-box > .public {
  width: 40px;
  border-radius: 0px 6px 0px 6px;
  background: rgb(255, 173, 72);
  color: #FFFFFF;
  z-index: 999;
  position: absolute;
  right: 20px;
  height: 22px;
}

.tag-scene-box > .scene-box > .img_box  >.el-image {
  top: 0;
  left: 0;
  width: 100%;
  height: 182px;
  border-radius: 6px;
}

.tag-scene-box >.scene-all {
  position: absolute;
  right: 25px;
  transform: translateY(-50%);
}

.image-slot >.el-icon {
  width: 300px;
  height: 200px;
  font-size: 100px;
  color: lightgray;
}
.red-btn{
  color: #ffffff;
  background: #BD0407;
}
</style>
