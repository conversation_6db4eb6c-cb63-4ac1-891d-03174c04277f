<template>
  <ScreenLayout  :leftDefaultTitle="menuTitle" :menuItems="menuItems" :selectedItemKey="selectedItemKey" :rightDefaultTitle="pageTitle" >
    <template #right-header-extra>
    </template>
    <template #right-content>
      <div style="padding-left: 20px;background-color: rgba(255, 255, 255, 1);">
        <el-tabs  class="demo-tabs" v-model="data.activeName"  @tab-click="handleTabsClick" >
          <el-tab-pane  label="场景详情"  name="info" key="0"></el-tab-pane>
          <el-tab-pane  label="场景阶段"  name="stage" key="1"></el-tab-pane>
          <el-tab-pane  label="马甲设置"  name="puppet" key="2"></el-tab-pane>
          <el-tab-pane  label="调查问卷"  name="question" key="3"></el-tab-pane>
        </el-tabs>
      </div>
      <div style="background-color: #ffffff; padding: 0 20px 20px 20px;">
        <div>
          <div v-if="data.showIndex == 0">
            <div>
              <span style="font-weight: 700;"> {{scene.sceneName}}</span>
              <span v-if="scene.sceneIspublic == 'Y'">(公共场景)</span>
            </div>
            <div class="img_box" @click="handleOpenScene(scene)">
              <el-image class="bigImg" :src="baseUrl + scene.sceneImage">
                <template #error>
                  <img src="/src/assets/images/no-png.png" alt="">
                </template>
              </el-image>
            </div>
            <div>{{scene.sceneIntroduction}}</div>
          </div>
          <div class="block" v-else-if="data.showIndex == 1">
            <div v-if="stageList.length>0">
              <ul data-v-e5cfd246="" class="el-timeline" style="margin-left: -32px;" >
                <li data-v-e5cfd246="" class="el-timeline-item" v-for="(stage, index) in stageList">
                  <div class="" style="    border-left: 2px dashed var(--el-timeline-node-color); height: 100%; left: 15px;  position: absolute;"></div>
                  <el-button type="primary" class="" style="width: 30px;height: 30px;align-items: center; border-radius: 50%; position: absolute;">{{index+1}}</el-button>
                  <div class="" style="margin-right: 20px;    padding-left: 30px;  position: relative;">
                    <div class="" style="    margin-bottom: 8px;font-size: 16px;margin-left: 6px;display: flex;padding-top: 3px;    font-weight: 700;"><div>{{stage.sceneStageTitle}}</div>
                      <div style="display: flex;">
                        <div>
                          <el-button link type="primary" icon="Edit" @click="handleEditStage(stage)" >修改</el-button>
                          <el-button link type="primary" icon="Delete" @click="handleDelStage(stage)" >删除</el-button>
                        </div>
                      </div>
                    </div>
                    <div class="">
                      <div class="" style="" >
                        {{ stage.sceneStageText }}
                      </div>
                    </div>
                  </div>
                </li>
              </ul>
              <div style=" margin-top: 20px;    padding-bottom: 30px;" >
                <el-button type="primary" @click="handleAddStage" >添加阶段</el-button>
              </div>
            </div>
            <div v-else>
              <div style="padding-bottom: 30px;" >
                <el-button type="primary" @click="handleAddStage" >添加阶段</el-button>
              </div>
              <el-empty  description="暂无数据"></el-empty>
            </div>
          </div>
          <div v-else-if="data.showIndex == 2" style="color: #434343;">
            <div v-if="puppetList.length == 0">
              <div>
                <el-button type="primary" @click="handleAddGroup" >添加小组</el-button>
              </div>
              <el-empty  description="暂无数据"></el-empty>
            </div>
            <div v-else>
              <div style="display: grid;grid-template-columns: 1fr 1fr;">
                <div v-for="(group, index) in puppetList" style="margin: 0 10px 10px 0; box-sizing: border-box;border: 1px solid rgb(232, 232, 232);border-radius: 8px;background: rgb(255, 255, 255); ">
                  <div style="padding: 0 20px 10px 20px;">
                    <div class="" style="margin-top: 20px;">
                      <div class="" >
                        <div style="font-size: 16px; display: flex; justify-content: space-between;font-weight: 700;">
                          <div >{{group.groupName}}</div>
                          <div style="display: flex;">
                            <div>
                              <el-button link type="primary" icon="Edit" @click="handleEditGroup(group)" >修改</el-button>
                              <el-button link type="primary" icon="Delete" @click="handleDelGroup(group)" >删除</el-button>
                            </div>
                          </div>
                        </div>
                      </div>
                      <el-tag  class="group-puppet-tag" v-for="(puppet, pupetIndex) in group.dcScenePuppetList"  closable :disable-transitions="false" size="large"  @click="showEditPuppet(puppet)" @close="handleDelPuppet(puppet)" >
                        <div style="display: flex;line-height: 16px;">
                          <img :src="'/dev-api/profile/puppet/' + puppet.puppetIcon + '.png'" style="height: 14px;width: 14px;" alt=""/>
                          <div>{{puppet.puppetName}}</div>
                        </div>
                      </el-tag>
                      <el-button class="button-new-tag group-puppet-tag" @click="showAddPuppet(group)">
                        + 添加马甲
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <el-button type="primary" @click="handleAddGroup" >添加小组</el-button>
              </div>
            </div>
          </div>
          <div v-else-if="data.showIndex == 3" style="padding-bottom: 20px;color: #434343;">
            <div v-if="testpaperList.length == 0">
              <div style="margin-left: 20px; " >
                <el-button type="primary" @click="handleAddTestpaper" >添加问卷</el-button>
              </div>
              <el-empty  description="暂无数据"></el-empty>
            </div>
            <div v-else>
              <div style="   display: grid;    grid-template-columns: 1fr 1fr;">
                <div v-for="(testpaper, testpaperIndex) in testpaperList" style="margin: 0 10px 10px 0; box-sizing: border-box;border: 1px solid rgb(232, 232, 232);border-radius: 8px;background: rgb(255, 255, 255); ">
                  <div style="padding: 0px 20px 10px 20px;">
                    <div class="" style="margin-top: 20px;">
                      <div class="" >
                        <div style="    font-size: 16px; display: flex; justify-content: space-between;    font-weight: 700;">
                          <div >{{testpaper.paperTitle}}</div>
                          <div style="display: flex;">
                            <div>
                              <el-button link type="primary" icon="Edit" @click="handleEditQuestion(testpaper)" >问题设置</el-button>
                              <el-button link type="primary" icon="Edit" @click="handleEditTestpaper(testpaper)" >修改</el-button>
                              <el-button link type="primary" icon="Delete" @click="handleDelTestpaper(testpaper)" >删除</el-button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div style="margin-left: 20px; " >
                <el-button type="primary" @click="handleAddTestpaper" >添加问卷</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </ScreenLayout>
  <!-- 添加或修改课程阶段对话框 -->
  <el-dialog :title="stageTitle" v-model="openStage" width="500px" append-to-body>
    <el-form ref="stageRef" :model="stageForm" :rules="rules" label-width="80px">
      <el-form-item label="序号" prop="sceneStageOrder">
        <el-input-number v-model="stageForm.sceneStageOrder" placeholder="请输入排序号" controls-position="right" :min="0" />
      </el-form-item>
      <el-form-item label="阶段名称" prop="sceneStageTitle">
        <el-input v-model="stageForm.sceneStageTitle" placeholder="请输入阶段名称" />
      </el-form-item>
      <el-form-item label="阶段描述" prop="sceneStageText">
        <el-input type="textarea" rows="10"  v-model="stageForm.sceneStageText" placeholder="请输入阶段描述" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitStageForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>

  <!--小组-->
  <el-dialog :title="groupTitle" v-model="groupOpen" width="500px" append-to-body>
    <el-form ref="stageRef" :model="groupForm" :rules="groupRules" label-width="80px">
      <el-form-item label="序号" prop="groupOrder">
        <el-input-number v-model="groupForm.groupOrder" placeholder="请输入排序号" controls-position="right" :min="0" />
      </el-form-item>
      <el-form-item label="小组名称" prop="groupName">
        <el-input v-model="groupForm.groupName" placeholder="请输入小组名称" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitGroupForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 添加或修改场景内的马甲对话框 -->
  <el-dialog :title="puppetTitle" v-model="puppetOpen" width="500px" append-to-body>
    <el-form ref="puppetRef" :model="puppetForm" :rules="rules" label-width="80px">
      <el-form-item label="序号" prop="puppetIndex">
        <el-input-number v-model="puppetForm.puppetIndex" placeholder="请输入排序号" controls-position="right" :min="0" />
      </el-form-item>
      <el-form-item label="名称" prop="puppetName">
        <el-input v-model="puppetForm.puppetName" placeholder="请输入马甲名称" />
      </el-form-item>
      <el-form-item label="图标" prop="icon">
        <el-popover
            placement="bottom-start"
            :width="540"
            trigger="click"
        >
          <template #reference>
            <el-input v-model="puppetForm.puppetIcon" placeholder="点击选择图标" @blur="showSelectIcon" readonly>
              <template #prefix>
                <img v-if="puppetForm.puppetIcon" :src="'/dev-api/profile/puppet/' + puppetForm.puppetIcon + '.png'" style="height: 20px;width: 16px;" alt=""/>
                <el-icon v-else style="height: 32px;width: 16px;"><search /></el-icon>
              </template>
            </el-input>
          </template>
          <IconPuppetSelect ref="iconSelectRef" @selected="selected" :active-icon="form.icon" />
        </el-popover>
      </el-form-item>
      <el-form-item label="描述" prop="puppetDesc">
        <el-input type="textarea" v-model="puppetForm.puppetDesc" placeholder="请输入描述" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitPuppetForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 添加或修改问卷调查对话框 -->
  <el-dialog :title="testpaperTitle" v-model="testpaperOpen" width="500px" append-to-body>
    <el-form ref="testpaperRef" :model="testpaperForm" :rules="rules" label-width="80px">
      <el-form-item label="问卷标题" prop="paperTitle">
        <el-input v-model="testpaperForm.paperTitle" placeholder="请输入问卷标题" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitTestpaperForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>

  <el-dialog :title="testpaperTitle" v-model="questionOpen" width="80%" top="5vh" append-to-body class="scrollbar">
    <el-empty v-if="questionList.length == 0"></el-empty>
    <draggable disabled="false" v-else v-model="questionList" >
      <template #item="{element,index}">
        <div  class="question-div" style="margin-bottom: 20px; display: flex;">
          <div style="width: 100%;">
            <div style="display: inline-flex;">
              <el-button v-if="index != 0 " link type="primary" icon="Top" @click="handleMoveQuestion(element,1)" >上移</el-button>
              <el-button v-if="index != questionList.length - 1" link type="primary" icon="updateDcSceneQuestionByTop" @click="handleMoveQuestion(element,2)" >下移</el-button>
              <el-button link type="primary" icon="Delete" @click="handleDelQuestion(element)" >删除</el-button>
            </div>
            <div style="display: flex;align-items: center;">
              <span>{{ index +1 }}.</span>
              <div style="width: 100%;">
                <el-input type="textarea" v-model="element.questonText" placeholder="标题" @blur="handleKeepQuestion(element)" />
              </div>
              <div style="display: inline-flex;width: 200px;">
                <span style="width: 40px;line-height: 32px;">分数</span>
                <div>
                  <el-input-number min="0" max="99" style="width: 100px;" v-model="element.questionPoint" @blur="handleKeepQuestion(element)"/>
                </div>
              </div>
            </div>
            <div v-if="element.questionType === '1' ">
              <el-radio-group  v-model="element.answers" @change="handleRadioGroupChange">
                <div class="radio-box" v-for="(item, itemIndex) in element.questionItem" style="display: flex;">
                  <el-radio :label="item.itemId">
                    <div style=" width: 100%;">
                      <el-input v-model="item.itemText" placeholder="选项，如本选项为正确选项请在最前面打勾" @change="handleKeepQuestionItem(item,element)" />
                    </div>
                  </el-radio>
                  <div style="display: inline-flex;">
                    <!--                      <el-button v-if="itemIndex > 0"  link type="primary" icon="Top" @click="handleEditTestpaper(testpaper)" >上移</el-button>-->
                    <!--                      <el-button v-if="itemIndex > 0 || (element.questionItem && element.questionItem.length > 1)"  link type="primary" icon="Bottom" @click="handleEditTestpaper(testpaper)" >下移</el-button>-->
                    <el-button v-if="itemIndex == element.questionItem.length-1" link type="primary" icon="Plus" @click="handleAddQuestionItem(element)" >新增</el-button>
                    <el-button v-if="itemIndex > 0" link type="primary" icon="Delete" @click="handleDelQuestionItem(item,element)" >删除</el-button>
                  </div>
                </div>
              </el-radio-group>
            </div>
            <div v-if="element.questionType === '2' " >
              <el-checkbox-group v-model="element.answers">
                <div v-for="(item, itemIndex) in element.questionItem" style="display: grid;margin-top: 5px;">
                  <el-checkbox :label="item.itemId" @change="handleCheckboxChange(item)" >
                    <div style="width: 100%;">
                      <el-input v-model="item.itemText"  placeholder="选项，如本选项为正确选项请在最前面打勾" @change="handleKeepQuestionItem(item,element)" />
                    </div>
                    <div style="display: inline-flex;">
                      <!--                      <el-button v-if="itemIndex > 0"  link type="primary" icon="Top" @click="handleEditTestpaper(item)" >上移</el-button>-->
                      <!--                      <el-button v-if="itemIndex > 0  || (element.questionItem && element.questionItem.length > 1)"  link type="primary" icon="Bottom" @click="handleEditTestpaper(item)" >下移</el-button>-->
                      <el-button v-if="itemIndex == element.questionItem.length-1" link type="primary" icon="Plus" @click="handleAddQuestionItem(element)" >新增</el-button>
                      <el-button v-if="itemIndex > 0" link type="primary" icon="Delete" @click="handleDelQuestionItem(item,element)" >删除</el-button>
                    </div>
                  </el-checkbox>
                </div>
              </el-checkbox-group>
            </div>

          </div>

        </div>
      </template>
    </draggable>
    <div>
      <div style="margin-top: 10px;">
        <el-button link type="primary" icon="Plus" @click="handleAddQuestion(testpaperId,1)" >添加单选题</el-button>
        <el-button link type="primary" icon="Plus" @click="handleAddQuestion(testpaperId,2)" >添加多选题</el-button>
        <el-upload
            :on-success="handleUploadSuccess"
            :file-list="fileList"
            :limit="1"
            :action="uploadFileUrl"
            :headers="headers"
            class="upload-file-uploader"
            ref="fileUpload"
        >
          <!-- 上传按钮 -->
          <el-button link type="primary" icon="Upload">导入问卷</el-button>
        </el-upload>
        <el-button link type="primary" icon="Download" @click="handleDownloadFile" ><a >下载导入模板</a></el-button>
      </div>
    </div>
    <template #footer>
      <el-button @click="handelQuestionOk">取消</el-button>
      <el-button type="primary" @click="handelQuestionOk">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup name="sceneInfo">
import ScreenLayout from "@/components/ScreenLayout/index.vue";
import IconPuppetSelect from "@/components/IconPuppetSelect";
import draggable from "vuedraggable";
import {
  listTestpaper,
  getTestpaper,
  delTestpaper,
  addTestpaper,
  updateTestpaper,
  download
} from "@/api/scenario/testpaper"
import {addStage, delStage, getStage, listStage, updateStage} from "@/api/scenario/stage.js";
import {
  addPuppet,
  delPuppet,
  getPuppet,
  getPuppetWithGroupBySceneId,
  updatePuppet
} from "../../../api/scenario/puppet.js";
import { listQuestion, getQuestion, delQuestion, addQuestion, updateQuestion,moveQuestion } from "@/api/scenario/question"
import {listItem, getItem, delItem, addItem, updateItem, changeSelect} from "@/api/scenario/questionitem"
import { getScene} from "@/api/scenario/scene.js";
import { Edit,Check,Search,Plus,Upload,Top } from '@element-plus/icons-vue'
import Link from "@/layout/components/Sidebar/Link.vue";
import {forEach} from "vuedraggable/dist/vuedraggable.common.js";
import {addGroup, delGroup, getGroup, updateGroup} from "@/api/scenario/group.js";
import FileUpload from "@/components/FileUpload/index.vue";
import {getToken} from "@/utils/auth.js";
import SvgIcon from "@/components/SvgIcon/index.vue";
import IconSelect from "@/components/IconSelect/index.vue";
import {ref} from "vue";
const pageTitle = ref("场景详情")
const menuTitle = ref("场景管理")
const menuItems = ref([
  { id: 1,name:"我的场景" },
  { id: 2,name:"我的课程" }
])
const route = useRoute();
const router = useRouter();
const scene = ref("")
const openStage = ref(false)
const stageTitle = ref("")


const { proxy } = getCurrentInstance()
const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + "/scenario/question/upload"); // 上传文件服务器地址
const downloadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + "/common/download"); // 上传文件服务器地址
const headers = ref({ Authorization: "Bearer " + getToken() });

const stageList = ref([])
const puppetList = ref([])
const testpaperList = ref([])
const questionList = ref([])
const open = ref(false)
const groupOpen = ref(false)
const puppetOpen = ref(false)
const testpaperOpen = ref(false)
const questionOpen = ref(false)

const fileList = ref([]);
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const groupTitle = ref("")
const puppetTitle = ref("")
const testpaperTitle = ref("")
const testpaperId = ref("")

// 上传请求路径
const field101Action = ref('/dev-api/scenario/question/upload')
// 上传文件列表
const field101fileList = ref([])

const data = reactive({
  showIndex: 0,
  form: {},
  stageForm: {},
  groupForm: {},
  puppetForm: {},
  testpaperForm: {},
  questionForm: {},
  puppetName: "",
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    paperTitle: null,
    sceneId: null,
    createUser: null,
    modifyUser: null,
    modifyTime: null
  },
  rules: {
  },
  maxRenNum: 0,
  activeName: 'info',
  activities: [{
    content: '支持使用图标',
    timestamp: '2018-04-12 20:46',
    size: 'large',
    type: 'primary',
    icon: 'el-icon-more'
  }, {
    content: '支持自定义颜色',
    timestamp: '2018-04-03 20:46',
    color: '#0bbd87'
  }, {
    content: '支持自定义尺寸',
    timestamp: '2018-04-03 20:46',
    size: 'large'
  }, {
    content: '默认样式的节点',
    timestamp: '2018-04-03 20:46'
  }]
})


const { queryParams, form,stageForm,groupForm,puppetForm,testpaperForm,questionForm, rules } = toRefs(data)
/**获取场景详情**/
function getSceneInfo(){
 const sceneId = route.params && route.params.sceneId
  getScene(sceneId).then(response=>{
    scene.value = response.data
  })
}


/** 选择图标 */
function selected(name) {
  puppetForm.value.puppetIcon = name;
}

/** 查询问列表 */

function getPuppetList() {
  loading.value = true
  const sceneId = route.params && route.params.sceneId
  getPuppetWithGroupBySceneId(sceneId).then(response => {
    puppetList.value = response.data
  })

}

// 取消按钮
function cancel() {
  open.value = false
  groupOpen.value = false
  puppetOpen.value = false
  testpaperOpen.value = false
  openStage.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {}
  groupForm.value = {}
  puppetForm.value = {}
  proxy.resetForm("sceneRef")
  proxy.resetForm("testpaperRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.paperId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

function handleTabsClick(tab, event) {
  if(tab.paneName == "info"){
    data.showIndex = 0;
    getSceneInfo()
  }else if(tab.paneName == "stage"){
    data.showIndex = 1;
    getStageList()
  }else if(tab.paneName == "puppet"){
    data.showIndex = 2;
    getPuppetList()
  }else if(tab.paneName == "question"){
    data.showIndex = 3;
    getTestpaperList()
  }
}

function handleAddGroup(){
  groupOpen.value = true
  groupTitle.value = "添加小组"
}

function handleDelGroup(group){
  const _groupIds = group.groupId
  proxy.$modal.confirm('是否确认删除？').then(function() {
    return delGroup(_groupIds)
  }).then(() => {
    getPuppetList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

function handleEditGroup(group){
  const _groupId = group.groupId
  getGroup(_groupId).then(response => {
    groupForm.value = response.data
    groupOpen.value = true
    title.value = "修改场景分组"
  })
}

function handleEditTestpaper(testpaper){
  const _paperId = testpaper.paperId
  getTestpaper(_paperId).then(response => {
    testpaperForm.value = response.data
    testpaperOpen.value = true
    testpaperTitle.value = "修改问卷调查"
  })
}

function handleEditQuestion(testpaper){
  getQuestionList(testpaper.paperId)
  questionOpen.value = true
  testpaperTitle.value = testpaper.paperTitle
  testpaperId.value = testpaper.paperId
  headers.value.paperId = testpaper.paperId
}

function getQuestionList(paperId){
  listQuestion({paperId: paperId}).then(response => {
    questionList.value = response.data
    questionList.value.forEach((question,index) => {
      let checkedList = []
      question.questionItem.forEach((item) =>{
        if(item.isRight == "Y"){
          checkedList.push(item.itemId)
        }
      })
      if(question.questionType == '1'){
        question.answers = checkedList.toString()
      }else if(question.questionType == '2'){
        question.answers = checkedList
      }
    })
  })
}



function submitGroupForm() {
  debugger
  if (groupForm.value.groupId != null) {
    updateGroup(groupForm.value).then(response => {
      proxy.$modal.msgSuccess("修改成功")
      groupOpen.value = false
      getPuppetList()
      reset()
    })
  } else {
    const sceneId = route.params && route.params.sceneId
    groupForm.value.sceneId = sceneId
    addGroup(groupForm.value).then(response => {
      proxy.$modal.msgSuccess("新增成功")
      groupOpen.value = false
      getPuppetList()
      reset()
    })
  }
}

function showAddPuppet(group){
  puppetOpen.value = true
  puppetTitle.value = "添加马甲"
  puppetForm.value.groupId = group.groupId
}

function showEditPuppet(puppet){
  const _puppetId = puppet.puppetId
  getPuppet(_puppetId).then(response => {
    puppetForm.value = response.data
    // puppetForm.value.groupId = group.groupId
    puppetOpen.value = true
    puppetTitle.value = "修改马甲"
  })
}

function handleDelPuppet(puppet) {
  const _puppetId = puppet.puppetId
  proxy.$modal.confirm('是否确认删除？').then(function() {
    return delPuppet(_puppetId)
  }).then(() => {
    getPuppetList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

function handleDelTestpaper(testpaper) {
  const _paperId = testpaper.paperId
  proxy.$modal.confirm('是否确认删除？').then(function() {
    return delTestpaper(_paperId)
  }).then(() => {
    getTestpaperList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}


function handleAddTestpaper(){
  testpaperOpen.value = true
  testpaperTitle.value = "添加问卷调查"
}

function getStageList() {
  loading.value = true
  const sceneId = route.params && route.params.sceneId
  listStage({sceneId: sceneId}).then(response => {
    stageList.value = response.data
    total.value = response.total
    loading.value = false
  })
}

function getTestpaperList() {
  loading.value = true
  const sceneId = route.params && route.params.sceneId
  listTestpaper({sceneId: sceneId}).then(response => {
    testpaperList.value = response.data
    loading.value = false
  })
}
/** 新增按钮操作 */
function handleAddStage() {
  debugger
  reset()
  openStage.value = true
  stageTitle.value = "添加场景阶段"
}

function handleEditStage(stage) {
  reset()
  const sceneStageId = stage.sceneStageId
  getStage(sceneStageId).then(response => {
    stageForm.value = response.data
    openStage.value = true
    stageTitle.value = "修改场景阶段"
  })
}

function handleDelStage(stage) {
  const sceneStageId = stage.sceneStageId
  proxy.$modal.confirm('是否确认删除所选数据项？').then(function() {
    return delStage(sceneStageId)
  }).then(() => {
    proxy.$modal.msgSuccess("删除成功")
    getStageList()()
  }).catch(() => {})
}

function submitStageForm() {
  proxy.$refs["stageRef"].validate(valid => {
    if (valid) {
      if (stageForm.value.sceneId != null) {
        updateStage(stageForm.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          openStage.value = false
          getStageList()
        })
      } else {
        stageForm.value.sceneId = route.params && route.params.sceneId
        addStage(stageForm.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          openStage.value = false
          getStageList()
        })
      }
    }
  })
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _paperId = row.paperId || ids.value
  getTestpaper(_paperId).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改问卷调查"
  })
}

function handleQuestion(row) {
  const paperId = row.paperId;
  router.push("/scenario/scene-question/index/" + paperId);
}

/** 提交按钮 */
function submitTestpaperForm() {
  proxy.$refs["testpaperRef"].validate(valid => {
    if (valid) {
      if (testpaperForm.value.paperId != null) {
        updateTestpaper(testpaperForm.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          testpaperOpen.value = false
          getTestpaperList()
        })
      } else {
        const sceneId = route.params && route.params.sceneId
        testpaperForm.value.sceneId = sceneId
        addTestpaper(testpaperForm.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          testpaperOpen.value = false
          getTestpaperList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _paperIds = row.paperId || ids.value
  proxy.$modal.confirm('是否确认删除问卷调查编号为"' + _paperIds + '"的数据项？').then(function() {
    return delTestpaper(_paperIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('scenario/testpaper/export', {
    ...queryParams.value
  }, `testpaper_${new Date().getTime()}.xlsx`)
}

function submitPuppetForm() {
  const sceneId = route.params && route.params.sceneId;
  puppetForm.value.sceneId = sceneId
  proxy.$refs["puppetRef"].validate(valid => {
    if (valid) {
      if (puppetForm.value.puppetId != null) {
        updatePuppet(puppetForm.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          puppetOpen.value = false
          reset()
          getPuppetList()
        })
      } else {
        puppetForm.value.puppetIndex = 999
        addPuppet(puppetForm.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          puppetOpen.value = false
          reset()
          getPuppetList()
        })
      }
    }
  })
}

function handleKeepQuestion(question){
  updateQuestion(question).then(response => {
    proxy.$modal.msgSuccess("修改成功")
    getQuestionList(question.paperId)
  })
}

function handleKeepQuestionItem(item,question){
  updateItem(item).then(response => {
    proxy.$modal.msgSuccess("修改成功")
    getQuestionList(question.paperId)
  })
}


function handleRadioGroupChange(value){
  changeSelect(value).then(response => {
    proxy.$modal.msgSuccess("操作成功")
    getQuestionList(question.paperId)
  })
}

function handleCheckboxChange(item){
  item.isRight = item.isRight === "Y"?"N":"Y"
  updateItem(item).then(response => {
    proxy.$modal.msgSuccess("操作成功")
    getQuestionList(question.paperId)
  })

}

function handleAddQuestion(paperId,type){
  questionForm.value.questonText = "问题"
  questionForm.value.questionType = type
  questionForm.value.paperId = paperId
  if(questionList.value.length == 0){
    questionForm.value.questionIndex = 0
  }else{
    questionForm.value.questionIndex = questionList.value[questionList.value.length-1].questionIndex +1
  }
  addQuestion(questionForm.value).then(response => {
    proxy.$modal.msgSuccess("新增成功")
    getQuestionList(paperId)
  })
}

function handleDelQuestion(question,paperId){
  proxy.$modal.confirm('是否确认删除？').then(function() {
    return delQuestion(question.questionId)
  }).then(() => {
    getQuestionList(question.paperId)
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

function handleMoveQuestion(question, type){
  moveQuestion({questionId: question.questionId,type: type}).then(response => {
    getQuestionList(question.paperId)
    proxy.$modal.msgSuccess("操作成功")
  })
}

function handleAddQuestionItem(question){
  const  itemLength = question.questionItem.length;
  let itemIndex = 0
  if(itemLength > 0){
    itemIndex = question.questionItem[itemLength-1].itemIndex
  }
  addItem({questionId: question.questionId,itemText: "",itemIndex: itemIndex+1}).then(response => {
    proxy.$modal.msgSuccess("新增成功")
    getQuestionList(question.paperId)
  })
}
function handleDelQuestionItem(item,question){
  proxy.$modal.confirm('是否确认删除？').then(function() {
    return delItem(item.itemId)
  }).then(() => {
    getQuestionList(question.paperId)
  }).catch(() => {})
}

function handelQuestionOk(){
  questionOpen.value = false
}

function handleUploadSuccess(res, file) {
  if (res.code === 200) {
    proxy.$modal.msgSuccess("上传成功")
  } else {
    proxy.$modal.msgSuccess("上传失败")
  }
  fileList.value = []
  getQuestionList(res.paperid)
}
function handleDownloadFile(){
  download(downloadFileUrl);
}
getSceneInfo()
</script>

<style>

.el-tabs__item:hover {
  color: #BD0407;
  cursor: pointer;
}
 .el-tabs__item.is-active {
   color: #BD0407;
 }
.el-tabs__active-bar {
  background-color: #BD0407;
}
</style>


<style scoped>
/* 局部样式 */
.stage-dw {
  padding-left: 4px;
  border-radius: 0px 4px 4px 0px;
  width: 24px;
  background-color: #C4C4C4;
  z-index: 999;
  margin-left: -22px;
}

.group-puppet-tag {
  width: auto;
  margin-top: 10px;
  margin-left: 5px;
}


.custom-icon-boy {
  background-image: url('/src/assets/icons/svg/box.png');
  background-size: cover;
  width: 16px;
  display: inline-block;
}
.custom-icon-girl {
  background-image: url('/src/assets/icons/svg/girl.png');
  background-size: cover;
  width: 16px;
  display: inline-block;
}
.question-div {
  border: 1px solid #dcdfe6;
  border-radius: var(--el-border-radius-base);
  padding: 15px;
}

</style>

<style >
.el-input__wrapper{
  padding-left: 1px !important;
  padding-right: 20px !important;
}
.el-input__wrapper > .el-input__inner {
  text-align: left;
}
.el-collapse,.el-collapse-item,.el-collapse-item__wrap,.el-collapse-item__header {
  border: none;
}
.el-overlay-dialog {
  text-align: center;
}

.radio-box {
  width: 100%;
  margin-top: 5px;
}

.el-radio {
  margin-right: 5px !important;
  width: 100%;
}
.el-checkbox {
  margin-right: 5px !important;
}
.el-radio__label {
  width: 100%;
}

.el-checkbox__label {
  width: 100%;
  display: flex;
}

.el-radio-group {
  width: -webkit-fill-available;
}
.el-input-number {
  width: 100%;
}
</style>
