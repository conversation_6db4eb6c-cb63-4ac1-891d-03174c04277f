<template>
  <!-- 头部栏组件 -->
  <ScreenLayout :leftDefaultTitle="menuTitle" :menuItems="menuItems" :selectedItemKey="selectedItemKey" :rightDefaultTitle="pageTitle">
    <template #right-header-extra>
      <div style="margin-left: auto;height: 17px;">
        <el-button icon="Tools" class="red-btn"  @click="handleAddCourse">添加课程</el-button>
      </div>
    </template>
    <template #right-content>
      <div>
        <div v-if="tagList.length === 0">
          <el-empty description="暂无数据"></el-empty>
        </div>
        <div class="tag-box" v-else v-for="(tag, index) in tagList" :key="tag.TAG_ID" >
          <div style="display: flex;">
            <div style="margin-bottom: 17px;
                      margin-top: 17px;
                      font-weight: 700;
                      line-height: 16px;" >{{ tag.TAG_NAME }}</div>
            <div class="top-right-btn" style="display: flex; align-items: center;margin-right: 12px">
              <a style="color: #93979C;font-size: 14px;" v-if="!tag.openall && openTag.indexOf(index) < 0 " @click="handleOpenAll(index)">展开全部</a>
              <a style="color: #93979C;font-size: 14px;" v-else @click="handleOpenAll(index)">收起</a>
            </div>
          </div>
          <div :class="tag.openall || openTag.indexOf(index) >= 0 ?'tag-scene-box':'tag-scene-box close'">
            <div class="scene-box" v-for="(course) in tag.coursesList" :key="course.courseId">
              <div class="img-title-box">
                <el-image class="img-box" :src="baseUrl + course.sceneImage">
                  <template #error>
                    <img src="/src/assets/images/no-png.png" alt="">
                  </template>
                </el-image>
                <a @click="handleOpenScene(scene)">{{course.courseName}}</a>
              </div>
              <div class="edit-box" >
                <div style="width: 100%; display: grid; grid-template-columns: 1fr 1fr;">
                  <el-button link type="primary" icon="Edit" @click="handleCourseSetting(course)" >课程设置</el-button>
                  <el-button link type="primary" icon="Edit" @click="handleEditSceneForTag(scene,tag)" >进入课堂</el-button>
                </div>
              </div>
            </div>
<!--            <div class="scene-box" >-->
<!--              <a @click="handleAddSceneForTag(tag)"-->
<!--                 class="add-scene">添加场景</a>-->
<!--            </div>-->
          </div>
        </div>
      </div>
    </template>
  </ScreenLayout>
  <!-- 添加或修改场景课程对话框 -->
  <el-dialog :title="courseTitle" v-model="courseOpen" width="500px" append-to-body>
    <el-form ref="courseRef" :model="courseForm" :rules="courseRules" label-width="80px">
      <el-form-item label="课程名称" prop="courseName">
        <el-input v-model="courseForm.courseName" placeholder="请输入课程名称" />
      </el-form-item>
      <el-form-item label="场景名称" prop="sceneId">
        <el-select v-model="courseForm.sceneId" placeholder="请选择场景">
          <el-option v-for="item in sceneList" :key="item.sceneId" :label="item.sceneName" :value="item.sceneId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="班级名称" prop="classCode">
        <el-select v-model="courseForm.classCode" placeholder="请选择班级">
          <el-option v-for="item in classList" :key="item.CLASS_CODE" :label="item.CLASS_NAME" :value="item.CLASS_CODE"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="课程介绍" prop="courseIntroduction">
        <el-input type="textarea" rows="10" v-model="courseForm.courseIntroduction" placeholder="请输入课程介绍" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitCourseForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>

</template>

<script setup name="Course">
import ScreenLayout from "@/components/ScreenLayout/index.vue";
import {addCourse, listClass, listCourse, updateCourse} from "@/api/scenario/course.js";
import {listScene} from "@/api/scenario/scene.js";
import {ref} from "vue";
const pageTitle = ref("我的课程")
const menuTitle = ref("场景管理")
const menuItems = ref([
  { id: 1,name:"我的场景" },
  { id: 2,name:"我的课程" }
])
const { proxy } = getCurrentInstance()
const router = useRouter();
const loading = ref(true)

const courseOpen = ref(false)
const courseTitle = ref("")

const tagList = ref([]);
const openTag = ref([])//展开的分组

const sceneList = ref([]);//我的场景下拉list
const classList = ref([]);//班级下拉list


const data = reactive({
  courseForm: {},
  courseRules: {
    courseName: [{ required: true, message: "课程名称不能为空", trigger: "blur" }],
    sceneId: [{ required: true, message: "场景不能为空", trigger: "blur" }],
    classCode: [{ required: true, message: "班级不能为空", trigger: "blur" }],
    courseIntroduction: [{ required: true, message: "课程介绍不能为空", trigger: "blur" }]
  },
})
const { courseForm,courseRules } = toRefs(data)




/** 查询场景课程列表 */
function getTagWithCourseList() {
  loading.value = true
  listCourse().then(response => {
    tagList.value = response.data
    loading.value = false
  })
}

// 表单重置
function reset() {
  courseForm.value = {}
  proxy.resetForm("courseRef")
}

/** 新增按钮操作 */
function handleAddCourse() {
  reset()
  listScene().then(response =>{
    sceneList.value = response.data
  })
  listClass().then(response =>{
    classList.value = response.data
  })
  courseOpen.value = true
  courseTitle.value = "添加课程"
}

// 取消按钮
function cancel() {
  courseOpen.value = false
  reset()
}

/** 课程提交按钮 */
function submitCourseForm() {
  proxy.$refs["courseRef"].validate(valid => {
    if (valid) {
      if (courseForm.value.courseId != null) {
        updateCourse(courseForm.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          courseOpen.value = false
          getTagWithCourseList()
        })
      } else {
        addCourse(courseForm.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          courseOpen.value = false
          getTagWithCourseList()
        })
      }
    }
  })
}

const handleOpenAll = (index) => {
  tagList.value[index].openall = !tagList.value[index].openall
  if(tagList.value[index].openall){
    openTag.value.push(index)
  }else{
    openTag.value.splice(openTag.value.indexOf(index), 1)
  }
}
function handleCourseSetting(course) {
  const courseId = course.courseId
  router.push("/scenario/course-info/" + courseId );
}

getTagWithCourseList()
</script>

<style>
.content-area {
  background-color: rgba(0, 0, 0, 0) !important;
  padding: 0 !important;
  border-radius: 0 !important;
}
.tag-box {
  background-color: #ffffff;
  margin-bottom: 10px;
  padding-left: 20px;
  .tag-scene-box.close {
    overflow: hidden;
    height: 230px;
  }
}
.tag-scene-box {
  flex-wrap: wrap;
  display: flex;
  width: calc(100% - 20px);
  .scene-box {
    display: grid;
    position: relative;
    height: 230px;
    width: 300px;
    text-align: center;
    margin-right: 10px;
    .img-title-box, .edit-box {
      grid-column: 1;    /* 使它们在同一列 */
      grid-row: 1;       /* 使它们在同一行 */
      position: relative; /* 或absolute，取决于具体布局需求 */
      z-index: 1;
      .img-box {
        width: 300px;
        height: 200px;
        border-radius: 6px;
        img {
          height: 100%;
          width: 100%;
        }
      }
    }
    .edit-box {
      display: none; /* 默认隐藏 */
      margin-top: 90px;
    }
  }
  .scene-box:hover .edit-box {
    display: block; /* 鼠标经过时显示 */
  }
}
.add-scene {
  height: 200px;
  width: 300px;
  float: left;
  border: 1px dashed rgba(221, 221, 221, 0.7);
  padding: 110px 0 0;
  line-height: 20px;
  font-size: 12px;
  text-align: center;
  color: #cbcbcb;
  background-position: center 70px;
  background-repeat: no-repeat;
  background-image: url(/src/assets/images/add.png);
  border-radius: 6px;
}
</style>

<style scoped>
/* 局部样式 */
.red-btn{
  color: #ffffff;
  background: #BD0407;
}
</style>
