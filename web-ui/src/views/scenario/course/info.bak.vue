<template>
  <div class="app-container">
    <div style="background-color: #ffffff;">
      <div style="margin-left: 20px;">
        <el-tabs v-model="data.activeName"  @tab-click="handleTabsClick" >
          <el-tab-pane  label="场景阶段"  name="stage" key="1"></el-tab-pane>
          <el-tab-pane  label="学员分组"  name="group" key="2"></el-tab-pane>
          <el-tab-pane  label="问卷与任务"  name="question" key="3"></el-tab-pane>
        </el-tabs>
      </div>
      <div class="block" v-if="data.showIndex == 0">
        <div v-if="stageList.length>0">
          <ul data-v-e5cfd246="" class="el-timeline" >
            <li data-v-e5cfd246="" class="el-timeline-item" v-for="(stage, index) in stageList">
              <div class="" style="    border-left: 2px dashed var(--el-timeline-node-color); height: 100%; left: 15px;  position: absolute;"></div>
              <el-button type="primary" class="" style="width: 30px;height: 30px;align-items: center; border-radius: 50%; position: absolute;">{{index+1}}</el-button>
              <div class="" style="margin-right: 20px;    padding-left: 30px;  position: relative;">
                <div class="" style="    margin-bottom: 8px;font-size: 16px;margin-left: 6px;display: flex;padding-top: 3px;    font-weight: 700;"><div>{{stage.sceneStageTitle}}</div>
                  <div style="display: flex;">
                    <div>
                      <el-button link type="primary" icon="Edit" @click="handleEditStage(stage)" >修改</el-button>
                      <el-button link type="primary" icon="Delete" @click="handleDelStage(stage)" >删除</el-button>
                    </div>
                  </div>
                </div>
                <div class="">
                  <div class="" style="" >
                    {{ stage.sceneStageText }}
                  </div>
                </div>
              </div>
            </li>
          </ul>
          <div style="margin-left: 20px;    margin-top: 20px;    padding-bottom: 30px;" >
            <el-button type="primary" @click="handleAddStage" >添加阶段</el-button>
          </div>
        </div>
        <div v-else>
          <div style="margin-left: 20px;padding-bottom: 30px;" >
            <el-button type="primary" @click="handleAddStage" >添加阶段</el-button>
          </div>
          <el-empty  description="暂无数据"></el-empty>
        </div>
      </div>
      <div v-else-if="data.showIndex == 1" style="padding-bottom: 20px;color: #434343;">
        <div style="display: flex;">
          <div style="padding-right: 10px;    border-right: 1px solid #E8E8E8;    width: 300px;">
            <div style="margin-left: 20px;">
              <div>
                <el-input>
                  <template #append>
                    <el-button :icon="Search" />
                  </template>
                </el-input>
              </div>
              <div class="demo-collapse" style="margin-top: 5px;">
                <el-collapse >
                  <el-collapse-item v-for="(courseTeGroup, groupIndex) in courseTeGroupList" :title="courseTeGroup.GROUP_NAME+'（'+courseTeGroup.USER_COUNT+'）'">
                    <div>
                      <VueDraggable  @end="studentDragableChane" ref="el" group="student" style="display: flex;" v-model="courseTeGroup.STUDENT_LIST" :id="courseTeGroup.groupId" :key="courseTeGroup.groupId" >
                          <div v-for="item in courseTeGroup.STUDENT_LIST" :key="item.RESOURCE_ID" style="margin-right: 4px;margin-top: 4px;border-radius: 4px;" >
                            <el-tag v-if="item.STUDENT_SEX ==='1' " ><i class="el-icon custom-icon-boy"></i><span>{{ item.STUDENT_NAME }}</span></el-tag>
                            <el-tag v-if="item.STUDENT_SEX ==='2' " type="danger"><i class="el-icon custom-icon-girl"></i><span>{{ item.STUDENT_NAME }}</span></el-tag>
                          </div>
                      </VueDraggable>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </div>
          <div style="width: 100%;">
            <div style="width: 100%;    display: grid;    grid-template-columns: 1fr 1fr;">
              <div style="display: contents;" >
                <div v-for="(courseGroup, index) in courseGroupList" style="margin: 0 10px 10px 10px; box-sizing: border-box;border: 1px solid rgb(232, 232, 232);border-radius: 8px;background: rgb(255, 255, 255); ">
                  <div style="padding: 0px 20px 10px 20px;">
                    <div class="" style="margin-top: 20px;">
                      <div class="" >
                        <div >
                          <div style="font-size: 16px; display: flex; justify-content: space-between;    font-weight: 700;">
                            <div>{{ courseGroup.groupName }}</div>
                            <div style="display: flex;">
                              <div>
                                <el-button link type="primary" icon="Edit" @click="handleEditCourseGroup(courseGroup)" >修改小组</el-button>
                                <el-button link type="primary" icon="Delete" @click="handleDelCourseGroup(courseGroup)" >删除小组</el-button>
                                <el-button link type="primary" icon="Plus" @click="showAddPuppet(courseGroup)" >添加马甲</el-button>
                              </div>
                            </div>
                          </div>
                          <div style="display: ruby;">
                            <div v-for="(coursePuppet,puppetIndex) in courseGroup.listCoursePuppet" style="width: max-content;border-radius: 4px 4px 4px 4px;border: 1px solid rgb(241, 241, 241);margin: 2px;text-align: center;   ">
                              <div style="justify-content: center; display: flex;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 114px;background: rgb(241, 241, 241);padding: 8px;" >
                                <div style="width: 80px;overflow: hidden; white-space: nowrap;text-overflow: ellipsis;" :title="coursePuppet.puppetName" @click="showEditPuppet(coursePuppet)">{{ coursePuppet.puppetName }}</div>
                                <div style=" height: 1em;width: 1em;" @click="handleDelPuppet(coursePuppet)"><Close/></div>
                              </div>
                              <div style="padding: 8px; display: grid;min-height: 36px;">
                                <VueDraggable @end="puppetDragableChane" group="student" v-model="coursePuppet.listDcCourseStudent" :key="coursePuppet.puppetId" :id="coursePuppet.puppetId" >
                                  <div v-for="item in coursePuppet.listDcCourseStudent" :key="item.RESOURCE_ID" style="margin-right: 4px;margin-top: 4px;border-radius: 4px;" >
                                    <el-tag v-if="item.studentSex ==='1' " closable  @close="handleDeleteCourseStudent(item)"><i class="el-icon custom-icon-boy"></i><span>{{ item.studentName }}</span></el-tag>
                                    <el-tag v-if="item.studentSex ==='2' " closable @close="handleDeleteCourseStudent(item)" type="danger"><i class="el-icon custom-icon-girl"></i><span>{{ item.studentName }}</span></el-tag>
                                  </div>
                                </VueDraggable>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div></div>
                    </div>
                  </div>
                </div>
              </div>

            </div>
            <div style="margin-left: 20px; " >
              <el-button type="primary" @click="handleAddCourseGroup" >添加小组</el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="block" v-else-if="data.showIndex == 2">
        <div>
          <el-row :gutter="1" class="mb8" style="margin-left: 10px;">
            <el-col :span="1.5">
              <el-button
                  type="primary"
                  plain
                  icon="Plus"
                  @click="handleAddQuestion"
              >问卷与任务下发</el-button>
            </el-col>
          </el-row>
          <el-table v-loading="loading" :data="courseList" @selection-change="handleSelectionChange">
            <el-table-column label="问卷名称" align="center" prop="courseCode" />
            <el-table-column label="下发时间" align="center" prop="courseName" />
            <el-table-column label="状态" align="center" prop="sceneId" />
            <el-table-column label="提交进度" align="center" prop="classCode" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template #default="scope">
                <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
                <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                <el-button link type="primary" icon="Setting" @click="handleSetting(scope.row)">设置</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <!-- 添加或修改课程阶段对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="stageRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="序号" prop="sceneStageOrder">
          <el-input type="number" v-model="form.sceneStageOrder" placeholder="请输入序号" />
        </el-form-item>
        <el-form-item label="阶段名称" prop="sceneStageTitle">
          <el-input v-model="form.sceneStageTitle" placeholder="请输入阶段名称" />
        </el-form-item>
        <el-form-item label="阶段描述" prop="sceneStageText">
          <el-input type="textarea" v-model="form.sceneStageText" placeholder="请输入阶段描述" />
        </el-form-item>
<!--        <el-form-item label="附件" prop="sceneAnnex">-->
<!--          <file-upload v-model="form.sceneAnnexList"/>-->
<!--        </el-form-item>-->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitStageForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog :title="groupTitle" v-model="groupOpen" width="500px" append-to-body>
      <el-form ref="stageRef" :model="groupForm" :rules="groupRules" label-width="80px">
        <el-form-item label="序号" prop="groupOrder">
          <el-input-number v-model="groupForm.groupOrder" placeholder="请输入排序号" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="小组名称" prop="groupName">
          <el-input v-model="groupForm.groupName" placeholder="请输入小组名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitCourseGroupForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加或修改课程内的马甲对话框 -->
    <el-dialog :title="puppetTitle" v-model="puppetOpen" width="500px" append-to-body>
      <el-form ref="puppetRef" :model="puppetForm" :rules="rules" label-width="80px">
        <el-form-item label="排序号" prop="puppetIndex">
          <el-input-number v-model="puppetForm.puppetIndex" placeholder="请输入排序号" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="马甲名称" prop="puppetName">
          <el-input v-model="puppetForm.puppetName" placeholder="请输入马甲名称" />
        </el-form-item>
        <el-form-item label="马甲图标" prop="icon">
          <el-popover
              placement="bottom-start"
              :width="540"
              trigger="click"
          >
            <template #reference>
              <el-input v-model="puppetForm.puppetIcon" placeholder="点击选择马甲图标" @blur="showSelectIcon" readonly>
                <template #prefix>
                  <img v-if="puppetForm.puppetIcon" :src="'/dev-api/profile/puppet/' + puppetForm.puppetIcon + '.png'" style="height: 20px;width: 16px;" alt=""/>
                  <el-icon v-else style="height: 32px;width: 16px;"><search /></el-icon>
                </template>
              </el-input>
            </template>
            <IconPuppetSelect ref="iconSelectRef" @selected="selected" :active-icon="form.icon" />
          </el-popover>
        </el-form-item>
        <el-form-item label="马甲描述" prop="puppetDesc">
          <el-input type="textarea" v-model="puppetForm.puppetDesc" placeholder="请输入马甲描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitCoursePuppetForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加或修改问卷调查对话框 -->
    <el-dialog :title="testpaperTitle" v-model="testpaperOpen" width="500px" append-to-body>
      <el-form ref="testpaperRef" :model="testpaperForm" :rules="rules" label-width="80px">
        <el-form-item label="问卷标题" prop="paperTitle">
          <el-input v-model="testpaperForm.paperTitle" placeholder="请输入问卷标题" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitTestpaperForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加或修改问卷对话框 -->
    <el-dialog :title="questionTitle" v-model="questionOpen" width="500px" append-to-body>
      <el-form ref="roleRef" :model="questionForm" :rules="rules" label-width="100px">
        <el-form-item label="类型">
          <el-radio-group @change="getToTreeselect" v-model="questionForm.questionType" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(70px, 1fr)); ">
            <el-radio label="1">问卷</el-radio>
            <el-radio label="2">任务</el-radio>
          </el-radio-group>
        </el-form-item>.
        <el-form-item label="问卷/任务" prop="roleName">
          <el-select v-model="form.roleName" placeholder="请选择问卷或任务" />
        </el-form-item>
        <el-form-item label="发布状态">
          <el-radio-group v-model="form.status" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(70px, 1fr)); ">
            <el-radio label="1">暂存</el-radio>
            <el-radio label="2">发布</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="发布范围">
          <el-tree
              class="tree-border"
              :data="treeOptions"
              show-checkbox
              ref="menuRef"
              node-key="id"
              empty-text="请先选择类型"
              :props="{ label: 'LABEL',id: 'ID', children: 'studentList' }"
          ></el-tree>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitQuestionForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Testpaper">
import { VueDraggable } from 'vue-draggable-plus'
import {
  listTestpaper,
  getTestpaper,
  delTestpaper,
  addTestpaper,
  updateTestpaper,
  download
} from "@/api/scenario/testpaper"
import {addStage, delStage, getStage, listStage, updateStage} from "@/api/scenario/stage.js";
import {
  addPuppet,
  delPuppet,
  getPuppet,
  getPuppetWithGroupBySceneId,
  updatePuppet
} from "../../../api/scenario/puppet.js";
import { listQuestion, getQuestion, delQuestion, addQuestion, updateQuestion,moveQuestion } from "@/api/scenario/question"
import {listItem, getItem, delItem, addItem, updateItem, changeSelect} from "@/api/scenario/questionitem"
import {delScene} from "@/api/scenario/scene.js";
import {Edit, Check, Search, Plus, Upload, Top, Close} from '@element-plus/icons-vue'
import Link from "@/layout/components/Sidebar/Link.vue";
import {forEach} from "vuedraggable/dist/vuedraggable.common.js";
import {listTeCourseGroup, listCourseGroup, addCourseGroup,updateCourseGroup,delCourseGroup, getCourseGroup} from "@/api/scenario/coursegroup.js";
import FileUpload from "@/components/FileUpload/index.vue";
import {getToken} from "@/utils/auth.js";
import {addCoursePuppet, delCoursePuppet, getCoursePuppet, updateCoursePuppet} from "@/api/scenario/coursepuppet.js";
import IconPuppetSelect from "@/components/IconPuppetSelect/index.vue";
import {addStudent,updateStudent,delStudent} from "@/api/scenario/student.js";
import {treeselect as menuTreeselect} from "@/api/system/menu.js";
import {getTree} from "@/api/scenario/paper.js";
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance()
const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + "/scenario/question/upload"); // 上传文件服务器地址
const downloadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + "/common/download"); // 上传文件服务器地址
const headers = ref({ Authorization: "Bearer " + getToken() });

const stageList = ref([])
const puppetList = ref([])
const testpaperList = ref([])
const questionList = ref([])
const courseGroupList = ref([])
const courseTeGroupList = ref([])
const open = ref(false)
const groupOpen = ref(false)
const puppetOpen = ref(false)
const testpaperOpen = ref(false)
const questionOpen = ref(false)

const fileList = ref([]);
const treeOptions = ref([]);
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const groupTitle = ref("")
const puppetTitle = ref("")
const questionTitle = ref("")
const testpaperId = ref("")

// 上传请求路径
const field101Action = ref('/dev-api/scenario/question/upload')
// 上传文件列表
const field101fileList = ref([])

const data = reactive({
  showIndex: 0,
  form: {},
  groupForm: {},
  puppetForm: {},
  testpaperForm: {},
  questionForm: {},
  puppetName: "",
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    paperTitle: null,
    sceneId: null,
    createUser: null,
    modifyUser: null,
    modifyTime: null
  },
  rules: {
  },
  maxRenNum: 0,
  activeName: 'stage',
  activities: [{
    content: '支持使用图标',
    timestamp: '2018-04-12 20:46',
    size: 'large',
    type: 'primary',
    icon: 'el-icon-more'
  }, {
    content: '支持自定义颜色',
    timestamp: '2018-04-03 20:46',
    color: '#0bbd87'
  }, {
    content: '支持自定义尺寸',
    timestamp: '2018-04-03 20:46',
    size: 'large'
  }, {
    content: '默认样式的节点',
    timestamp: '2018-04-03 20:46'
  }]
})


const { queryParams, form,groupForm,puppetForm,testpaperForm,questionForm, rules } = toRefs(data)

function handleChange(a){
  debugger
}

function getToTreeselect(type) {
  const courseId = route.params && route.params.courseId
  getTree(type,courseId).then(response => {
    debugger
    treeOptions.value = response.data;
  });
}

function studentDragableChane(event){
  addStudent({studentCode: event.clonedData.STUDENT_CODE,studentIndex: event.newIndex, puppetId: event.to.id}).then(() => {
    proxy.$modal.msgSuccess("操作成功")
    getTeGroupList()
  }).catch(() => {
  });
}

function puppetDragableChane(event){
  let student = event.clonedData
  student.puppetId =  event.to.id
  student.studentIndex = event.newIndex
  updateStudent(student).then(() => {
    proxy.$modal.msgSuccess("操作成功")
    getTeGroupList()
  }).catch(() => {
  });
}

function selected(name) {
  puppetForm.value.puppetIcon = name;
}

/** 查询问列表 */

function getPuppetList() {
  loading.value = true
  const sceneId = route.params && route.params.sceneId
  getPuppetWithGroupBySceneId(sceneId).then(response => {
    puppetList.value = response.data
    data.showIndex = 1;
  })

}

// 取消按钮
function cancel() {
  open.value = false
  groupOpen.value = false
  puppetOpen.value = false
  testpaperOpen.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
  }
  groupForm.value = {

  }
  puppetForm.value = {

  }

  proxy.resetForm("sceneRef")

  proxy.resetForm("testpaperRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.paperId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

function handleTabsClick(tab, event) {
  debugger
  if(tab.paneName == "stage"){
    data.showIndex = 0;
  }else if(tab.paneName == "group"){
    data.showIndex = 1;
    getTeGroupList()
  }else if(tab.paneName == "question"){
    data.showIndex = 2;
    getTestpaperList()
  }
}
function getTeGroupList(){
  const courseId = route.params && route.params.courseId
  listTeCourseGroup(courseId).then(response => {
    courseTeGroupList.value = response.data
  })
  listCourseGroup(courseId).then(response => {
    courseGroupList.value = response.data
  })
}


function handleDelCourseGroup (group){
  const _groupIds = group.groupId
  proxy.$modal.confirm('是否确认删除？').then(function() {
    return delCourseGroup(_groupIds)
  }).then(() => {
    getTeGroupList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

function handleEditCourseGroup(group){
  const _groupId = group.groupId
  getCourseGroup(_groupId).then(response => {
    groupForm.value = response.data
    groupOpen.value = true
    title.value = "修改场景分组"
  })
}

function handleDeleteCourseStudent(student){
  proxy.$modal.confirm('是否确认删除？').then(function() {
    return delStudent(student.studentId)
  }).then(() => {
    getTeGroupList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

function handleEditTestpaper(testpaper){
  const _paperId = testpaper.paperId
  getTestpaper(_paperId).then(response => {
    testpaperForm.value = response.data
    testpaperOpen.value = true
    testpaperTitle.value = "修改问卷调查"
  })
}


function getQuestionList(paperId){
  queryParams.value.paperId = paperId
  listQuestion(queryParams.value).then(response => {
    questionList.value = response.rows
    questionList.value.forEach((question,index) => {
      let checkedList = []
      question.questionItem.forEach((item) =>{
        if(item.isRight == "Y"){
          checkedList.push(item.itemId)
        }
      })
      if(question.questionType == '1'){
        question.answers = checkedList.toString()
      }else if(question.questionType == '2'){
        question.answers = checkedList
      }
    })
  })
}



function submitCourseGroupForm() {
  if (groupForm.value.groupId != null) {
    updateCourseGroup(groupForm.value).then(response => {
      proxy.$modal.msgSuccess("修改成功")
      groupOpen.value = false
      getTeGroupList()
      reset()
    })
  } else {
    const courseId = route.params && route.params.courseId
    groupForm.value.courseId = courseId
    addCourseGroup(groupForm.value).then(response => {
      proxy.$modal.msgSuccess("新增成功")
      groupOpen.value = false
      getTeGroupList()
      reset()
    })
  }
}

function handleAddCourseGroup(){
  groupOpen.value = true
  groupTitle.value = "添加小组"
}

function showAddPuppet(group){
  puppetOpen.value = true
  puppetTitle.value = "添加马甲"
  puppetForm.value.groupId = group.groupId
}

function showEditPuppet(puppet){
  const _puppetId = puppet.puppetId
  getCoursePuppet(_puppetId).then(response => {
    puppetForm.value = response.data
    // puppetForm.value.groupId = group.groupId
    puppetOpen.value = true
    puppetTitle.value = "修改马甲"
  })
}

function handleDelPuppet(puppet) {
  const _puppetId = puppet.puppetId
  proxy.$modal.confirm('是否确认删除？').then(function() {
    return delCoursePuppet(_puppetId)
  }).then(() => {
    getTeGroupList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

function handleDelTestpaper(testpaper) {
  const _paperId = testpaper.paperId
  proxy.$modal.confirm('是否确认删除？').then(function() {
    return delTestpaper(_paperId)
  }).then(() => {
    getTestpaperList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}


function handleAddTestpaper(){
  testpaperOpen.value = true
  testpaperTitle.value = "添加问卷调查"
}

function getStageList() {
  loading.value = true
  queryParams.value.sceneId = route.params && route.params.sceneId
  listStage(queryParams.value).then(response => {
    stageList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

function getTestpaperList() {
  loading.value = true
  queryParams.value.sceneId = route.params && route.params.sceneId
  listTestpaper(queryParams.value).then(response => {
    testpaperList.value = response.rows
    loading.value = false
  })
}
/** 新增按钮操作 */
function handleAddStage() {
  reset()
  open.value = true
  title.value = "添加场景阶段"
}

function handleEditStage(stage) {
  reset()
  const sceneStageId = stage.sceneStageId
  getStage(sceneStageId).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改场景阶段"
  })
}

function handleDelStage(stage) {
  const sceneStageId = stage.sceneStageId
  proxy.$modal.confirm('是否确认删除所选数据项？').then(function() {
    return delStage(sceneStageId)
  }).then(() => {
    proxy.$modal.msgSuccess("删除成功")
    getStageList()()
  }).catch(() => {})
}
function submitStageForm() {
  proxy.$refs["stageRef"].validate(valid => {
    if (valid) {
      if (form.value.courseStageId != null) {
        updateStage(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getStageList()
        })
      } else {
        const sceneId = route.params && route.params.sceneId;
        form.value.sceneId = sceneId
        addStage(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getStageList()
        })
      }
    }
  })
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _paperId = row.paperId || ids.value
  getTestpaper(_paperId).then(response => {
    form.value = response.data
    open.value = true
    title.value = "修改问卷调查"
  })
}

function handleQuestion(row) {
  const paperId = row.paperId;
  router.push("/scenario/scene-question/index/" + paperId);
}

/** 提交按钮 */
function submitTestpaperForm() {
  proxy.$refs["testpaperRef"].validate(valid => {
    if (valid) {
      if (testpaperForm.value.paperId != null) {
        updateTestpaper(testpaperForm.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          testpaperOpen.value = false
          getTestpaperList()
        })
      } else {
        const sceneId = route.params && route.params.sceneId
        testpaperForm.value.sceneId = sceneId
        addTestpaper(testpaperForm.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          testpaperOpen.value = false
          getTestpaperList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _paperIds = row.paperId || ids.value
  proxy.$modal.confirm('是否确认删除问卷调查编号为"' + _paperIds + '"的数据项？').then(function() {
    return delTestpaper(_paperIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('scenario/testpaper/export', {
    ...queryParams.value
  }, `testpaper_${new Date().getTime()}.xlsx`)
}

function submitCoursePuppetForm() {
  // const sceneId = route.params && route.params.sceneId;
  // puppetForm.value.sceneId = sceneId
  debugger
  proxy.$refs["puppetRef"].validate(valid => {
    if (valid) {
      if (puppetForm.value.puppetId != null) {
        updateCoursePuppet(puppetForm.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          puppetOpen.value = false
          getTeGroupList()
        })
      } else {
        addCoursePuppet(puppetForm.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          puppetOpen.value = false
          getTeGroupList()
        })
      }
    }
  })
}

function handleKeepQuestion(question){
  updateQuestion(question).then(response => {
    proxy.$modal.msgSuccess("修改成功")
    getQuestionList(question.paperId)
  })
}

function handleKeepQuestionItem(item,question){
  updateItem(item).then(response => {
    proxy.$modal.msgSuccess("修改成功")
    getQuestionList(question.paperId)
  })
}


function handleRadioGroupChange(value){
  changeSelect(value).then(response => {
    proxy.$modal.msgSuccess("操作成功")
    getQuestionList(question.paperId)
  })
}

function handleCheckboxChange(item){
  item.isRight = item.isRight === "Y"?"N":"Y"
  updateItem(item).then(response => {
    proxy.$modal.msgSuccess("操作成功")
    getQuestionList(question.paperId)
  })

}

function handleAddQuestion(){
  questionOpen.value = true
  questionTitle.value = "问卷与任务下发"
}

function handleDelQuestion(question,paperId){
  proxy.$modal.confirm('是否确认删除？').then(function() {
    return delQuestion(question.questionId)
  }).then(() => {
    getQuestionList(question.paperId)
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

function handleMoveQuestion(question, type){
  moveQuestion({questionId: question.questionId,type: type}).then(response => {
    getQuestionList(question.paperId)
    proxy.$modal.msgSuccess("操作成功")
  })
}

function handleAddQuestionItem(question){
  const  itemLength = question.questionItem.length;
  let itemIndex = 0
  if(itemLength > 0){
    itemIndex = question.questionItem[itemLength-1].itemIndex
  }
  addItem({questionId: question.questionId,itemText: "",itemIndex: itemIndex+1}).then(response => {
    proxy.$modal.msgSuccess("新增成功")
    getQuestionList(question.paperId)
  })
}
function handleDelQuestionItem(item,question){
  proxy.$modal.confirm('是否确认删除？').then(function() {
    return delItem(item.itemId)
  }).then(() => {
    getQuestionList(question.paperId)
  }).catch(() => {})
}

function handleUploadSuccess(res, file) {
  if (res.code === 200) {
    proxy.$modal.msgSuccess("上传成功")
  } else {
    proxy.$modal.msgSuccess("上传失败")
  }
  fileList.value = []
  getQuestionList(res.paperid)
}
function handleDownloadFile(){
  download(downloadFileUrl);
}

getStageList()
</script>


<style scoped>
/* 局部样式 */
.stage-dw {
  padding-left: 4px;
  border-radius: 0px 4px 4px 0px;
  width: 24px;
  background-color: #C4C4C4;
  z-index: 999;
  margin-left: -22px;
}

.group-puppet-tag {
  width: auto;
  margin-top: 10px;
  margin-left: 5px;
}


.custom-icon-boy {
  background-image: url('/src/assets/icons/svg/box.png');
  background-size: cover;
  width: 16px;
  display: inline-block;
}
.custom-icon-girl {
  background-image: url('/src/assets/icons/svg/girl.png');
  background-size: cover;
  width: 16px;
  display: inline-block;
}
.question-div {
  border: 1px solid #dcdfe6;
  border-radius: var(--el-border-radius-base);
  padding: 15px;
}

</style>

<style >
.el-timeline-item__wrapper {
  margin-right: 20px;
}
.el-input__wrapper{
  padding-left: 1px !important;
  padding-right: 20px !important;
}
.el-input__wrapper > .el-input__inner {
  text-align: left;
}
.el-collapse,.el-collapse-item,.el-collapse-item__wrap,.el-collapse-item__header {
  border: none;
}
.el-overlay-dialog {
  text-align: center;
}

.radio-box {
  width: 100%;
  margin-top: 5px;
}

.el-radio {
  margin-right: 5px !important;
  width: 100%;
}
.el-checkbox {
  margin-right: 5px !important;
}
.el-radio__label {
  width: 100%;
}

.el-input-number {
  width: 100%;
}

.el-checkbox__label {
  width: 100%;
  display: flex;
}

.el-radio-group {
  width: -webkit-fill-available;
}
</style>
