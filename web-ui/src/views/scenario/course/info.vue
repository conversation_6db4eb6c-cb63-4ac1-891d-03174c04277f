<template>
  <ScreenLayout :leftDefaultTitle="menuTitle" :menuItems="menuItems" :selectedItemKey="selectedItemKey" :rightDefaultTitle="pageTitle">
    <template #right-header-extra></template>
    <template #right-content>
      <div style="padding-left: 20px;background-color: rgba(255, 255, 255, 1);">
        <el-tabs v-model="activeName"  @tab-click="handleTabsClick" >
          <el-tab-pane  label="课程详情"  name="info" key="0"></el-tab-pane>
          <el-tab-pane  label="课程阶段"  name="stage" key="1"></el-tab-pane>
          <el-tab-pane  label="课程马甲"  name="puppet" key="2"></el-tab-pane>
          <el-tab-pane  label="调查问卷"  name="question" key="3"></el-tab-pane>
        </el-tabs>
      </div>
      <div style="background-color: #ffffff; padding: 0 20px 20px 20px;">
        <div>
          <div v-if="showIndex === '0'">
            <div :title="courseInfo.courseIntroduction" @click="handleUpdateCourse(courseInfo)">
              <span style="font-weight: 700;"> {{courseInfo.courseName}}</span>
            </div>
          </div>
          <div v-else-if="showIndex === '1'">
            <div v-if="stageList.length > 0">
              <ul data-v-e5cfd246="" class="el-timeline" style="margin-left: -32px;" >
                <li data-v-e5cfd246="" class="el-timeline-item" v-for="(stage, index) in stageList">
                  <div class="" style="    border-left: 2px dashed var(--el-timeline-node-color); height: 100%; left: 15px;  position: absolute;"></div>
                  <el-button type="primary" class="" style="width: 30px;height: 30px;align-items: center; border-radius: 50%; position: absolute;">{{index+1}}</el-button>
                  <div class="" style="margin-right: 20px;    padding-left: 30px;  position: relative;">
                    <div class="" style=" margin-bottom: 8px;font-size: 16px;margin-left: 6px;display: flex;padding-top: 3px;    font-weight: 700;">
                      <div style="width: 200px;">{{stage.courseStageTitle}}</div>
                      <div style="display: flex;">
                        <div>
                          <el-button link type="primary" icon="Edit" @click="handleEditStage(stage)" >修改</el-button>
                          <el-button link type="primary" icon="Delete" @click="handleDelStage(stage)" >删除</el-button>
                        </div>
                      </div>
                    </div>
                    <div class="">
                      <div class="" style="" >
                        {{ stage.courseStageText }}
                      </div>
                    </div>
                  </div>
                </li>
              </ul>
              <div style=" margin-top: 20px;    padding-bottom: 30px;" >
                <el-button type="primary" @click="handleAddStage" >添加阶段</el-button>
              </div>
            </div>
            <div v-else>
              <div style="padding-bottom: 30px;" >
                <el-button type="primary" @click="handleAddStage" >添加阶段</el-button>
              </div>
              <el-empty  description="暂无数据"></el-empty>
            </div>
          </div>
          <div v-else-if="showIndex === '2'">
            <div style="display: flex;">
              <div style="border-right: 1px solid #E8E8E8;width: 300px;">
                <div>
<!--                  <div>-->
<!--                    <el-input>-->
<!--                      <template #append>-->
<!--                        <el-button :icon="Search" />-->
<!--                      </template>-->
<!--                    </el-input>-->
<!--                  </div>-->
                  <div>教学系统分组</div>
                  <div class="demo-collapse" style="margin-top: 5px;">
                    <el-collapse >
                      <el-collapse-item v-for="(courseTeGroup, groupIndex) in courseTeGroupList" :title="courseTeGroup.GROUP_NAME+'（'+courseTeGroup.USER_COUNT+'）'">
                        <div>
                          <VueDraggable  @end="studentDragableChane" ref="el" group="student" style="display: flex;" v-model="courseTeGroup.STUDENT_LIST" :id="courseTeGroup.groupId" :key="courseTeGroup.groupId" >
                            <div v-for="item in courseTeGroup.STUDENT_LIST" :key="item.RESOURCE_ID" style="margin-right: 4px;margin-top: 4px;border-radius: 4px;" >
                              <el-tag v-if="item.STUDENT_SEX ==='1' " ><i class="el-icon custom-icon-boy"></i><span>{{ item.STUDENT_NAME }}</span></el-tag>
                              <el-tag v-if="item.STUDENT_SEX ==='2' " type="danger"><i class="el-icon custom-icon-girl"></i><span>{{ item.STUDENT_NAME }}</span></el-tag>
                            </div>
                          </VueDraggable>
                        </div>
                      </el-collapse-item>
                    </el-collapse>
                  </div>
                </div>
              </div>
              <div style="width: 100%;">
                <div style="width: 100%;display: grid;grid-template-columns: 1fr 1fr;">
                  <div style="display: contents;" >
                    <div v-for="(courseGroup, index) in courseGroupList" style="margin: 0 10px 10px 10px; box-sizing: border-box;border: 1px solid rgb(232, 232, 232);border-radius: 8px;background: rgb(255, 255, 255); ">
                      <div style="padding: 0px 20px 20px 10px;">
                        <div style="margin-top: 20px;">
                          <div>
                            <div>
                              <div style="font-size: 16px; display: flex; justify-content: space-between;    font-weight: 700;">
                                <div>{{ courseGroup.groupName }}</div>
                                <div style="display: flex;">
                                  <div>
                                    <el-button link type="primary" icon="Edit" @click="handleEditCourseGroup(courseGroup)" >修改</el-button>
                                    <el-button link type="primary" icon="Delete" @click="handleDelCourseGroup(courseGroup)" >删除</el-button>
                                    <el-button link type="primary" icon="Plus" @click="showAddPuppet(courseGroup)" >添加马甲</el-button>
                                  </div>
                                </div>
                              </div>
                              <div style="display: ruby;">
                                <div v-for="(coursePuppet,puppetIndex) in courseGroup.listCoursePuppet" style="width: max-content;border-radius: 4px 4px 4px 4px;border: 1px solid rgb(241, 241, 241);margin: 2px;text-align: center;   ">
                                  <div style="justify-content: center; display: flex;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;width: 114px;background: rgb(241, 241, 241);padding: 8px;" >
                                    <div style="width: 80px;overflow: hidden; white-space: nowrap;text-overflow: ellipsis;" :title="coursePuppet.puppetName" @click="showEditPuppet(coursePuppet)">{{ coursePuppet.puppetName }}</div>
                                    <div style=" height: 1em;width: 1em;" @click="handleDelPuppet(coursePuppet)"><Close/></div>
                                  </div>
                                  <div style="padding: 8px; display: grid;min-height: 36px;">
                                    <VueDraggable @end="puppetDragableChane" group="student" v-model="coursePuppet.listDcCourseStudent" :key="coursePuppet.puppetId" :id="coursePuppet.puppetId" >
                                      <div v-for="item in coursePuppet.listDcCourseStudent" :key="item.RESOURCE_ID" style="margin-right: 4px;margin-top: 4px;border-radius: 4px;" >
                                        <el-tag v-if="item.studentSex ==='1' " closable  @close="handleDeleteCourseStudent(item)"><i class="el-icon custom-icon-boy"></i><span>{{ item.studentName }}</span></el-tag>
                                        <el-tag v-if="item.studentSex ==='2' " closable @close="handleDeleteCourseStudent(item)" type="danger"><i class="el-icon custom-icon-girl"></i><span>{{ item.studentName }}</span></el-tag>
                                      </div>
                                    </VueDraggable>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div style="margin-left: 20px; " >
                  <el-button type="primary" @click="handleAddCourseGroup" >添加小组</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <template  #fab>
      <div></div>
    </template>
  </ScreenLayout>
  <!-- 添加或修改场景课程对话框 -->
  <el-dialog :title="courseTitle" v-model="courseOpen" width="500px" append-to-body>
    <el-form ref="courseRef" :model="courseForm" :rules="courseRules" label-width="80px">
      <el-form-item label="课程名称" prop="courseName">
        <el-input v-model="courseForm.courseName" placeholder="请输入课程名称" />
      </el-form-item>
      <el-form-item label="课程介绍" prop="courseIntroduction">
        <el-input type="textarea" rows="10" v-model="courseForm.courseIntroduction" placeholder="请输入课程介绍" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitCourseForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 添加或修改课程阶段对话框 -->
  <el-dialog :title="stageTitle" v-model="stageOpen" width="500px" append-to-body>
    <el-form ref="stageRef" :model="stageForm" :rules="stageRules" label-width="80px">
      <el-form-item label="序号" prop="courseStageOrder">
        <el-input-number v-model="stageForm.courseStageOrder" placeholder="请输入排序号" controls-position="right" :min="0" />
      </el-form-item>
      <el-form-item label="阶段名称" prop="courseStageTitle">
        <el-input v-model="stageForm.courseStageTitle" placeholder="请输入阶段名称" />
      </el-form-item>
      <el-form-item label="阶段描述" prop="courseStageText">
        <el-input type="textarea" rows="10"  v-model="stageForm.courseStageText" placeholder="请输入阶段描述" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitStageForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>

  <!--小组-->
  <el-dialog :title="groupTitle" v-model="groupOpen" width="500px" append-to-body>
    <el-form ref="groupRef" :model="groupForm" :rules="groupRules" label-width="80px">
      <el-form-item label="序号" prop="groupOrder">
        <el-input-number v-model="groupForm.groupOrder" placeholder="请输入排序号" controls-position="right" :min="0" />
      </el-form-item>
      <el-form-item label="小组名称" prop="groupName">
        <el-input v-model="groupForm.groupName" placeholder="请输入小组名称" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitCourseGroupForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 添加或修改课程内的马甲对话框 -->
  <el-dialog :title="puppetTitle" v-model="puppetOpen" width="500px" append-to-body>
    <el-form ref="puppetRef" :model="puppetForm" :rules="puppetRules" label-width="80px">
      <el-form-item label="排序号" prop="puppetIndex">
        <el-input-number v-model="puppetForm.puppetIndex" placeholder="请输入排序号" controls-position="right" :min="0" />
      </el-form-item>
      <el-form-item label="马甲名称" prop="puppetName">
        <el-input v-model="puppetForm.puppetName" placeholder="请输入马甲名称" />
      </el-form-item>
      <el-form-item label="马甲图标" prop="puppetIcon">
        <el-popover
            placement="bottom-start"
            :width="540"
            trigger="click"
        >
          <template #reference>
            <el-input v-model="puppetForm.puppetIcon" placeholder="点击选择马甲图标" @blur="showSelectIcon" readonly>
              <template #prefix>
                <img v-if="puppetForm.puppetIcon" :src="'/dev-api/profile/puppet/' + puppetForm.puppetIcon + '.png'" style="height: 20px;width: 16px;" alt=""/>
                <el-icon v-else style="height: 32px;width: 16px;"><search /></el-icon>
              </template>
            </el-input>
          </template>
          <IconPuppetSelect ref="iconSelectRef" @selected="selected" :active-icon="puppetForm.icon" />
        </el-popover>
      </el-form-item>
      <el-form-item label="马甲描述" prop="puppetDesc">
        <el-input type="textarea" v-model="puppetForm.puppetDesc" placeholder="请输入马甲描述" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitCoursePuppetForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="CourseInfo">
import ScreenLayout from "@/components/ScreenLayout/index.vue";
import IconPuppetSelect from "@/components/IconPuppetSelect";
import { VueDraggable } from 'vue-draggable-plus'
import {ref} from "vue";
import {getCourse} from "@/api/scenario/course.js";
import {listStage,getStage,delStage,addStage,updateStage} from "@/api/scenario/coursestage.js";
import {
  addCourseGroup,
  delCourseGroup, getCourseGroup,
  listCourseGroup,
  listTeCourseGroup,
  updateCourseGroup
} from "../../../api/scenario/coursegroup";
import {
  addCoursePuppet,
  delCoursePuppet,
  getCoursePuppet,
  updateCoursePuppet
} from "../../../api/scenario/coursepuppet";
import {addStudent, updateStudent} from "../../../api/scenario/student";
import {addCourse, updateCourse} from "../../../api/scenario/course";
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API );
const loading = ref(true)
const { proxy } = getCurrentInstance()
const route = useRoute();
const pageTitle = ref("课程设置")
const menuTitle = ref("场景管理")
const menuItems = ref([
  { id: 1,name:"我的场景" },
  { id: 2,name:"我的课程" }
])

const activeName = ref("info")
const showIndex = ref("0")

const courseInfo = ref("")
const courseTitle = ref("")
const courseOpen = ref(false)

const stageList = ref([])
const stageOpen = ref(false)
const stageTitle = ref("")

const courseTeGroupList = ref([])
const courseGroupList = ref([])
const groupOpen = ref(false)
const groupTitle = ref("")

const puppetOpen = ref(false)
const puppetTitle = ref("")
const iconSelectRef = ref(null);

const data = reactive({
  courseForm: {},
  courseRules: {
    courseName: [{ required: true, message: "课程名称不能为空", trigger: "blur" }],
    sceneId: [{ required: true, message: "场景不能为空", trigger: "blur" }],
    classCode: [{ required: true, message: "班级不能为空", trigger: "blur" }],
    courseIntroduction: [{ required: true, message: "课程介绍不能为空", trigger: "blur" }]
  },
  stageForm: {},
  stageRules: {
    courseStageOrder: [{ required: true, message: "排序号不能为空", trigger: "blur" }],
    courseStageTitle: [{ required: true, message: "阶段名称不能为空", trigger: "blur" }],
    courseStageText: [{ required: true, message: "阶段描述不能为空", trigger: "blur" }]
  },
  groupForm: {},
  groupRules: {
    groupOrder: [{ required: true, message: "排序号不能为空", trigger: "blur" }],
    groupName: [{ required: true, message: "小组名称不能为空", trigger: "blur" }]
  },
  puppetForm: {},
  puppetRules: {
    puppetIndex: [{ required: true, message: "排序号不能为空", trigger: "blur" }],
    puppetName: [{ required: true, message: "马甲名称不能为空", trigger: "blur" }],
    puppetIcon: [{ required: true, message: "马甲图标不能为空", trigger: "blur" }],
    puppetDesc: [{ required: true, message: "马甲描述不能为空", trigger: "blur" }]
  }

})
const { courseForm,courseRules,stageForm,stageRules,groupForm,groupRules,puppetForm,puppetRules } = toRefs(data)

// 取消按钮
function cancel() {
  stageOpen.value = false
  groupOpen.value = false
  puppetOpen.value = false
  courseOpen.value = false
  reset()
}
// 表单重置
function reset() {
  stageForm.value = {
  }
  groupForm.value = {
  }
  puppetForm.value = {
  }
  courseForm.value = {
  }
  proxy.resetForm("sceneRef")
  proxy.resetForm("groupRef")
  proxy.resetForm("puppetRef")
  proxy.resetForm("courseRef")
}

function getCourseInfo(){
  const courseId = route.params && route.params.courseId
  getCourse(courseId).then(response => {
    courseInfo.value = response.data
  })
}

/** 修改按钮操作 */
function handleUpdateCourse(course) {
  reset()
  const _courseId = course.courseId
  getCourse(_courseId).then(response => {
    courseForm.value = response.data
    courseOpen.value = true
    courseTitle.value = "修改课程"
  })
}
/** 课程提交按钮 */
function submitCourseForm() {
  proxy.$refs["courseRef"].validate(valid => {
    if (valid) {
      if (courseForm.value.courseId != null) {
        updateCourse(courseForm.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          courseOpen.value = false
          getCourseInfo()
        })
      } else {
        addCourse(courseForm.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          courseOpen.value = false
          getCourseInfo()
        })
      }
    }
  })
}

function handleTabsClick(tab) {
  if(tab.paneName === "info"){
    showIndex.value = '0';
    getCourseInfo()
  }else if(tab.paneName === "stage"){
    showIndex.value = '1';
    getStageList()
  }else if(tab.paneName === "puppet"){
    showIndex.value = '2';
    getGroupList()
  }else if(tab.paneName === "question"){
    showIndex.value = '3';
    getTestpaperList()
  }
}

function getStageList() {
  loading.value = true
  const courseId = route.params && route.params.courseId
  listStage({courseId: courseId}).then(response => {
    stageList.value = response.data
    loading.value = false
  })
}
function handleEditStage(stage){
  getStage(stage.courseStageId).then(response =>{
    stageForm.value = response.data
    stageOpen.value = true
  })
}
/** 新增按钮操作 */
function handleAddStage() {
  reset()
  stageOpen.value = true
  stageTitle.value = "添加场景阶段"
}
function handleDelStage(stage) {
  const courseStageId = stage.courseStageId
  proxy.$modal.confirm('是否确认删除所选数据项？').then(function() {
    return delStage(courseStageId)
  }).then(() => {
    proxy.$modal.msgSuccess("删除成功")
    getStageList()
  }).catch(() => {})
}
function submitStageForm() {
  proxy.$refs["stageRef"].validate(valid => {
    if (valid) {
      if (stageForm.value.courseStageId != null) {
        updateStage(stageForm.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          stageOpen.value = false
          getStageList()
        })
      } else {
        const courseId = route.params && route.params.courseId;
        stageForm.value.courseId = courseId
        addStage(stageForm.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          stageOpen.value = false
          getStageList()
        })
      }
    }
  })
}

function getGroupList(){
  loading.value = true
  const courseId = route.params && route.params.courseId
  listTeCourseGroup(courseId).then(response => {
    courseTeGroupList.value = response.data
    loading.value = false
  })
  listCourseGroup(courseId).then(response => {
    courseGroupList.value = response.data
    loading.value = false
  })
}
function handleAddCourseGroup(){
  groupOpen.value = true
  groupTitle.value = "添加小组"
}
function submitCourseGroupForm() {
  proxy.$refs["groupRef"].validate(valid => {
    if (valid) {
      if (groupForm.value.groupId != null) {
        updateCourseGroup(groupForm.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          groupOpen.value = false
          getGroupList()
          reset()
        })
      } else {
        const courseId = route.params && route.params.courseId
        groupForm.value.courseId = courseId
        addCourseGroup(groupForm.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          groupOpen.value = false
          getGroupList()
          reset()
        })
      }
    }
  })
}

function handleDelCourseGroup (group){
  const _groupIds = group.groupId
  proxy.$modal.confirm('是否确认删除？').then(function() {
    return delCourseGroup(_groupIds)
  }).then(() => {
    getGroupList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

function handleEditCourseGroup(group){
  const _groupId = group.groupId
  getCourseGroup(_groupId).then(response => {
    groupForm.value = response.data
    groupOpen.value = true
    groupTitle.value = "修改场景分组"
  })
}
function showAddPuppet(group){
  puppetOpen.value = true
  puppetTitle.value = "添加马甲"
  puppetForm.value.groupId = group.groupId
}
function showEditPuppet(puppet){
  const _puppetId = puppet.puppetId
  getCoursePuppet(_puppetId).then(response => {
    puppetForm.value = response.data
    puppetOpen.value = true
    puppetTitle.value = "修改马甲"
  })
}

function handleDelPuppet(puppet) {
  const _puppetId = puppet.puppetId
  proxy.$modal.confirm('是否确认删除？').then(function() {
    return delCoursePuppet(_puppetId)
  }).then(() => {
    getGroupList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}
/** 展示下拉图标 */
function showSelectIcon() {
  iconSelectRef.value.reset();
}

function selected(name) {
  puppetForm.value.puppetIcon = name;
}
function submitCoursePuppetForm() {
  proxy.$refs["puppetRef"].validate(valid => {
    if (valid) {
      if (puppetForm.value.puppetId != null) {
        updateCoursePuppet(puppetForm.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          puppetOpen.value = false
          getGroupList()
          reset()
        })
      } else {
        addCoursePuppet(puppetForm.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          puppetOpen.value = false
          getGroupList()
          reset()
        })
      }
    }
  })
}
function studentDragableChane(event){
  addStudent({studentCode: event.clonedData.STUDENT_CODE,studentIndex: event.newIndex, puppetId: event.to.id}).then(() => {
    proxy.$modal.msgSuccess("操作成功")
    getGroupList()
  }).catch(() => {
  });
}
function puppetDragableChane(event){
  let student = event.clonedData
  student.puppetId =  event.to.id
  student.studentIndex = event.newIndex
  updateStudent(student).then(() => {
    proxy.$modal.msgSuccess("操作成功")
    getGroupList()
  }).catch(() => {
  });
}


getCourseInfo()
</script>

<style>
.el-tabs__item:hover {
  color: #BD0407;
  cursor: pointer;
}
.el-tabs__item.is-active {
  color: #BD0407;
}
.el-tabs__active-bar {
  background-color: #BD0407;
}
.el-input-number {
  width: 100%;
}
.el-input__inner {
  text-align: left !important;
}
</style>
